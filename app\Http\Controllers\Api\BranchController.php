<?php

namespace App\Http\Controllers\Api;

use App\Models\Branch;
use App\Models\Status;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\BranchResource;
use App\Http\Resources\BranchCollection;
use App\Http\Requests\BranchStoreRequest;
use App\Http\Requests\BranchUpdateRequest;

class BranchController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Branch::class);

        $search = $request->get('search', '');

        $branches = Branch::search($search)
            ->with(['status', 'createdBy', 'updatedBy'])
            ->latest()
            ->paginate();

        return new BranchCollection($branches);
    }

    /**
     * @param \App\Http\Requests\BranchStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(BranchStoreRequest $request)
    {
        $this->authorize('create', Branch::class);

        $validated = $request->validated();

        $branch = Branch::create($validated);

        return new BranchResource($branch->load(['status', 'createdBy', 'updatedBy']));
    }

    /**
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function show(Branch $branch)
    {
        $this->authorize('view', $branch);

        return new BranchResource($branch->load(['status', 'createdBy', 'updatedBy']));
    }

    /**
     * @param \App\Http\Requests\BranchUpdateRequest $request
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function update(BranchUpdateRequest $request, Branch $branch)
    {
        $this->authorize('update', $branch);

        $validated = $request->validated();

        $branch->update($validated);

        return new BranchResource($branch->load(['status', 'createdBy', 'updatedBy']));
    }

    /**
     * @param \App\Models\Branch $branch
     * @return \Illuminate\Http\Response
     */
    public function destroy(Branch $branch)
    {
        $this->authorize('delete', $branch);

        $branch->delete();

        return response()->noContent();
    }

    /**
     * Get all active branches for dropdowns
     */
    public function active(Request $request)
    {
        $branches = Branch::select('id', 'name', 'address', 'phone', 'email')
            ->where('status_id', 1) // Only active branches
            ->orderBy('name')
            ->get();

        return response()->json($branches);
    }

    /**
     * Get statuses for branch forms
     */
    public function statuses()
    {
        $statuses = Status::whereIn("id", [1,2])->pluck('name', 'id');
        return response()->json($statuses);
    }
}
