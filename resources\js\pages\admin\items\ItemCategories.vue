<template>
  <AdminListTemplate
    title="Item Categories"
    subtitle="Manage item categories and classifications"
    :loading="loading"
    :error="error"
    :items="categories"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Category"
    empty-state-title="No categories found"
    empty-state-message="Get started by adding your first item category."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @view="handleView"
    @edit="handleEdit"
    @delete="handleDelete"
    @refresh="fetchCategories"
  >
    <template #filters>
      <Select
        v-model="filters.status"
        :options="statusOptions"
        placeholder="Filter by status"
        class="w-48"
        @change="applyFilters"
      />
    </template>
  </AdminListTemplate>

  <!-- Delete Confirmation Modal -->
  <AdminModal
    v-model="showDeleteModal"
    title="Delete Category"
    :loading="deleting"
    @confirm="confirmDelete"
    @cancel="showDeleteModal = false"
  >
    <p class="text-gray-600">
      Are you sure you want to delete this category? This action cannot be undone.
    </p>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminModal } from '@/components/admin/ui';
import { Select } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const categories = ref([]);
const selectedItems = ref([]);
const showDeleteModal = ref(false);
const deleting = ref(false);
const itemToDelete = ref<any>(null);

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(25);

// Filters
const filters = reactive({
  search: '',
  status: '',
  sort_by: 'name',
  sort_direction: 'asc'
});

// Table columns
const columns = [
  { key: 'name', label: 'Category Name', sortable: true },
  { key: 'description', label: 'Description', sortable: false },
  { key: 'items_count', label: 'Items', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Options
const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' }
];

// Methods
const fetchCategories = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    categories.value = [
      {
        id: 1,
        name: 'Electronics',
        description: 'Electronic devices and gadgets',
        items_count: 45,
        status: 'active',
        created_at: '2024-01-15'
      },
      {
        id: 2,
        name: 'Furniture',
        description: 'Home and office furniture',
        items_count: 23,
        status: 'active',
        created_at: '2024-01-10'
      }
    ];
    
    totalItems.value = categories.value.length;
    totalPages.value = Math.ceil(totalItems.value / perPage.value);
  } catch (err) {
    error.value = 'Failed to load categories';
    console.error('Error fetching categories:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  fetchCategories();
};

const handleCreate = () => {
  router.push('/admin-spa/items/categories/create');
};

const handleView = (category: any) => {
  router.push(`/admin-spa/items/categories/view/${category.id}`);
};

const handleEdit = (category: any) => {
  router.push(`/admin-spa/items/categories/edit/${category.id}`);
};

const handleDelete = (category: any) => {
  itemToDelete.value = category;
  showDeleteModal.value = true;
};

const confirmDelete = async () => {
  if (!itemToDelete.value) return;
  
  deleting.value = true;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    showNotification({
      type: 'success',
      title: 'Category Deleted',
      message: 'Category has been successfully deleted.'
    });
    
    await fetchCategories();
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'Delete Failed',
      message: 'Failed to delete category. Please try again.'
    });
  } finally {
    deleting.value = false;
    showDeleteModal.value = false;
    itemToDelete.value = null;
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  applyFilters();
};

const handleSort = (column: string, direction: string) => {
  filters.sort_by = column;
  filters.sort_direction = direction;
  applyFilters();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchCategories();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? [...categories.value] : [];
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  // TODO: Implement bulk delete
  showNotification({
    type: 'info',
    title: 'Bulk Delete',
    message: 'Bulk delete functionality will be implemented soon.'
  });
};

// Initialize
onMounted(() => {
  fetchCategories();
});
</script>
