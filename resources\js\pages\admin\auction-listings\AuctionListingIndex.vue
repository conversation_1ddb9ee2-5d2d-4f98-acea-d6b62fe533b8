<template>
  <AdminListTemplate
    title="Auction Listings"
    subtitle="Manage auction listings and bidding sessions"
    :loading="loading"
    :error="error"
    :items="auctionListings"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Auction Listing"
    empty-state-title="No auction listings found"
    empty-state-message="Start by creating a new auction listing."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchAuctionListings"
  >
    <template #actions>
      <Button
        variant="outline"
        @click="handleCreateItem"
        class="mr-2"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Add Item
      </Button>
    </template>

    <template #rows="{ items }">
      <tr
        v-for="auctionListing in items"
        :key="auctionListing.id"
        class="hover:bg-gray-50"
      >
        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-16 w-16">
              <img
                :src="auctionListing.image || '/assets/img/160x160/img2.jpg'"
                :alt="auctionListing.name"
                class="h-16 w-16 rounded-lg object-cover"
              />
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">
                <a
                  :href="`/auction-listing/${auctionListing.id}`"
                  class="hover:text-blue-600"
                >
                  {{ auctionListing.name || '-' }}
                </a>
              </div>
            </div>
          </div>
        </td>

        <!-- Available Items -->
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <Badge variant="primary">
            {{ getAvailableItemsCount(auctionListing) }}
          </Badge>
        </td>

        <!-- Start Date -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ formatDate(auctionListing.date_from) }}
        </td>

        <!-- End Date -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ formatDate(auctionListing.date_to) }}
        </td>

        <!-- Description -->
        <td class="px-6 py-4 text-sm text-gray-900">
          <span
            class="truncate block max-w-xs"
            :title="auctionListing.description"
          >
            {{ truncateText(auctionListing.description, 50) }}
          </span>
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(auctionListing)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(auctionListing)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(auctionListing)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';
import type { AuctionType } from '@/types';

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const auctionListings = ref<AuctionType[]>([]);

// Filters
const filters = ref({
  search: ''
});

// Table columns
const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'items_count', label: 'Available Items', sortable: false },
  { key: 'date_from', label: 'Start Date', sortable: true },
  { key: 'date_to', label: 'End Date', sortable: true },
  { key: 'description', label: 'Description', sortable: false }
];

// Methods
const fetchAuctionListings = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/auction-listings', params);
    auctionListings.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch auction listings';
    console.error('Error fetching auction listings:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchAuctionListings();
};

const handleCreate = () => {
  router.push('/admin-spa/auction-listings/create');
};

const handleCreateItem = () => {
  router.push('/admin-spa/items/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAuctionListings();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? auctionListings.value.map(a => a.id.toString()) : [];
};

const handleEdit = (auctionListing: AuctionType) => {
  router.push(`/admin-spa/auction-listings/edit/${auctionListing.id}`);
};

const handleView = (auctionListing: AuctionType) => {
  router.push(`/admin-spa/auction-listings/view/${auctionListing.id}`);
};

const handleDelete = async (auctionListing: AuctionType) => {
  if (confirm('Are you sure you want to delete this auction listing?')) {
    try {
      await api.delete(`/auction-types/${auctionListing.id}`);
      showNotification('Auction listing deleted successfully', 'success');
      await fetchAuctionListings();
    } catch (err) {
      showNotification('Failed to delete auction listing', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} auction listings?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} auction listings deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctionListings();
    } catch (err) {
      showNotification('Failed to delete auction listings', 'error');
    }
  }
};

// Utility methods
const getAvailableItemsCount = (auctionListing: AuctionType) => {
  return auctionListing.items?.filter(item => !item.closed_by).length || 0;
};

const formatDate = (dateString: string | undefined) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const truncateText = (text: string | undefined, limit: number) => {
  if (!text) return '-';
  return text.length > limit ? text.substring(0, limit) + '...' : text;
};

// Lifecycle
onMounted(() => {
  fetchAuctionListings();
});
</script>
