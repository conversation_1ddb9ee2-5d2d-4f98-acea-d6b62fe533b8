

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Auction Listings</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end gap-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AuctionType::class)): ?>
                <a href="<?php echo e(route('auction-listing.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Auction Listing
                </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
                <a href="/items/create" class="btn btn-outline-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Item
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">All Auction Listings</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="ps-4">Name</th>
                            <th>Available Items</th>                    
                            <th>Start Date</th>                    
                            <th>End Date</th>
                            <th>Description</th>
                            <th class="text-center" style="width: 134px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $auctionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auctionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="ps-4">
                                <a class="d-flex align-items-center" href="/auction-listing/<?php echo e($auctionType->id); ?>">
                                    <div class="flex-shrink-0">
                                        <img class="avatar avatar-lg" src="<?php echo e($auctionType->image ?? asset('assets/img/160x160/img2.jpg')); ?>" alt="Image Description">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0"><?php echo e($auctionType->name ?? '-'); ?></h5>
                                    </div>
                                </a>
                            </td>
                            <td>
                                <span class="badge bg-soft-primary"><?php echo e(_number($auctionType->items()->whereNull('closed_by')->count())); ?></span>
                            </td>
                            <td><?php echo e(\Carbon\Carbon::parse($auctionType->date_from)->format('M d, Y')); ?></td>
                            <td><?php echo e(\Carbon\Carbon::parse($auctionType->date_to)->format('M d, Y')); ?></td>
                            <td>
                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?php echo e($auctionType->description); ?>">
                                    <?php echo e(Str::limit($auctionType->description, 50) ?? '-'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="d-flex justify-content-center gap-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $auctionType)): ?>
                                    <a href="<?php echo e(route('auction-listing.edit', $auctionType)); ?>" class="btn btn-sm btn-white">
                                        <i class="bi bi-pencil-square text-primary"></i>
                                    </a>
                                    <?php endif; ?> 
                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $auctionType)): ?>
                                    <a href="<?php echo e(route('auction-listing.show', $auctionType)); ?>" class="btn btn-sm btn-white">
                                        <i class="bi bi-eye text-info"></i>
                                    </a>
                                    <?php endif; ?> 
                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $auctionType)): ?>
                                    <form action="<?php echo e(route('auction-listing.destroy', $auctionType)); ?>" 
                                          method="POST" 
                                          onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-white">
                                            <i class="bi bi-trash text-danger"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center p-4">
                                <div class="d-flex flex-column align-items-center py-5">
                                    <i class="bi bi-calendar-x fs-1 text-muted mb-2"></i>
                                    <h5>No auction listings found</h5>
                                    <p class="text-muted">Start by creating a new auction listing.</p>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\AuctionType::class)): ?>
                                    <a href="<?php echo e(route('auction-listing.create')); ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-plus-circle me-1"></i> Add Auction Listing
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/auction_listing/index.blade.php ENDPATH**/ ?>