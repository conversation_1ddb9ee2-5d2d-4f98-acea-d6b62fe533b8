<template>
  <AdminDetailTemplate
    title="Auction Details"
    subtitle="View auction information and bids"
    :loading="loading"
    :error="error"
    @back="handleBack"
  >
    <div v-if="auction" class="space-y-6">
      <!-- Auction Overview -->
      <Card>
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Auction Overview</h3>
            <AdminBadge :variant="getStatusVariant(auction.status)">
              {{ auction.status }}
            </AdminBadge>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500">Title</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ auction.title }}</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Starting Price</dt>
              <dd class="mt-1 text-sm text-gray-900">${{ auction.starting_price }}</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Current Bid</dt>
              <dd class="mt-1 text-sm text-gray-900">${{ auction.current_bid }}</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Start Date</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ formatDate(auction.start_date) }}</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">End Date</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ formatDate(auction.end_date) }}</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Total Bids</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ auction.total_bids }}</dd>
            </div>
          </div>
          
          <div class="mt-6">
            <dt class="text-sm font-medium text-gray-500">Description</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ auction.description }}</dd>
          </div>
        </div>
      </Card>

      <!-- Recent Bids -->
      <Card>
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Bids</h3>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bidder
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="bid in auction.recent_bids" :key="bid.id">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ bid.bidder_name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${{ bid.amount }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatDateTime(bid.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <AdminBadge :variant="getBidStatusVariant(bid.status)">
                      {{ bid.status }}
                    </AdminBadge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div v-if="auction.recent_bids.length === 0" class="text-center py-8">
            <p class="text-gray-500">No bids yet</p>
          </div>
        </div>
      </Card>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-4">
        <Button
          variant="outline"
          @click="handleBack"
        >
          Back to List
        </Button>
        
        <Button
          variant="outline"
          @click="handleEdit"
        >
          Edit Auction
        </Button>
        
        <Button
          v-if="auction.status === 'draft'"
          @click="handlePublish"
        >
          Publish Auction
        </Button>
        
        <Button
          v-if="auction.status === 'active'"
          variant="danger"
          @click="handleEnd"
        >
          End Auction
        </Button>
      </div>
    </div>
  </AdminDetailTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminDetailTemplate } from '@/components/admin/templates';
import { AdminBadge } from '@/components/admin/ui';
import { Card, Button } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const auction = ref<any>(null);

// Methods
const fetchAuction = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const auctionId = route.params.id;
    
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    auction.value = {
      id: auctionId,
      title: 'Vintage Watch Collection',
      description: 'A collection of rare vintage watches from the 1920s-1950s',
      starting_price: '150.00',
      current_bid: '275.00',
      start_date: '2024-02-01T10:00:00Z',
      end_date: '2024-02-15T18:00:00Z',
      status: 'active',
      total_bids: 12,
      recent_bids: [
        {
          id: 1,
          bidder_name: 'John Smith',
          amount: '275.00',
          created_at: '2024-02-10T14:30:00Z',
          status: 'active'
        },
        {
          id: 2,
          bidder_name: 'Jane Doe',
          amount: '250.00',
          created_at: '2024-02-10T12:15:00Z',
          status: 'outbid'
        },
        {
          id: 3,
          bidder_name: 'Bob Johnson',
          amount: '225.00',
          created_at: '2024-02-09T16:45:00Z',
          status: 'outbid'
        }
      ]
    };
  } catch (err) {
    error.value = 'Failed to load auction details';
    console.error('Error fetching auction:', err);
  } finally {
    loading.value = false;
  }
};

const handleBack = () => {
  router.push('/admin-spa/auctions/list');
};

const handleEdit = () => {
  router.push(`/admin-spa/auctions/edit/${auction.value.id}`);
};

const handlePublish = async () => {
  try {
    // TODO: Implement publish logic
    showNotification({
      type: 'success',
      title: 'Auction Published',
      message: 'Auction has been successfully published.'
    });
    
    await fetchAuction();
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'Publish Failed',
      message: 'Failed to publish auction. Please try again.'
    });
  }
};

const handleEnd = async () => {
  try {
    // TODO: Implement end auction logic
    showNotification({
      type: 'success',
      title: 'Auction Ended',
      message: 'Auction has been successfully ended.'
    });
    
    await fetchAuction();
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'End Failed',
      message: 'Failed to end auction. Please try again.'
    });
  }
};

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'ended': return 'secondary';
    case 'draft': return 'warning';
    default: return 'secondary';
  }
};

const getBidStatusVariant = (status: string) => {
  switch (status) {
    case 'active': return 'success';
    case 'outbid': return 'secondary';
    default: return 'secondary';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

// Initialize
onMounted(() => {
  fetchAuction();
});
</script>
