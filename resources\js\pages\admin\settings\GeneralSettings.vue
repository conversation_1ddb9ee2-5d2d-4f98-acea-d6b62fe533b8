<template>
  <AdminListTemplate
    title="General Settings"
    subtitle="Configure general system settings"
    :loading="loading"
    :error="error"
    :items="settings"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Setting"
    empty-state-title="No settings found"
    empty-state-message="Start by creating a new system setting."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchSettings"
  >
    <template #rows="{ items }">
      <tr
        v-for="setting in items"
        :key="setting.id"
        class="hover:bg-gray-50"
      >
        <!-- Key Column -->
        <td class="px-6 py-4">
          <div class="text-sm font-medium text-gray-900">
            {{ setting.key || '-' }}
          </div>
        </td>

        <!-- Value Column -->
        <td class="px-6 py-4">
          <div class="text-sm text-gray-900">
            <div v-if="setting.path" class="flex items-center space-x-2">
              <img
                v-if="isImage(setting.path)"
                :src="setting.path"
                alt="Setting image"
                class="h-8 w-8 object-cover rounded"
              />
              <a
                :href="setting.path"
                target="_blank"
                class="text-blue-600 hover:text-blue-800 text-sm"
              >
                View File
              </a>
            </div>
            <div v-else class="max-w-xs">
              <span
                class="truncate block"
                :title="setting.value"
              >
                {{ truncateText(setting.value, 50) }}
              </span>
            </div>
          </div>
        </td>

        <!-- Created By -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ setting.created_by_user?.name || 'System' }}
        </td>

        <!-- Created At -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ formatDate(setting.created_at) }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(setting)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(setting)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(setting)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface Setting {
  id: number;
  key: string;
  value: string;
  path?: string;
  created_by?: number;
  updated_by?: number;
  user_id?: number;
  school_id?: number;
  created_by_user?: {
    name: string;
  };
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const settings = ref<Setting[]>([]);

// Filters
const filters = ref({
  search: ''
});

// Table columns
const columns = [
  { key: 'key', label: 'Setting Key', sortable: true },
  { key: 'value', label: 'Value', sortable: false },
  { key: 'created_by', label: 'Created By', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true }
];

// Methods
const fetchSettings = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/admin/settings', params);
    settings.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch settings';
    console.error('Error fetching settings:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchSettings();
};

const handleCreate = () => {
  router.push('/admin-spa/settings/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchSettings();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? settings.value.map(s => s.id.toString()) : [];
};

const handleEdit = (setting: Setting) => {
  router.push(`/admin-spa/settings/edit/${setting.id}`);
};

const handleView = (setting: Setting) => {
  router.push(`/admin-spa/settings/view/${setting.id}`);
};

const handleDelete = async (setting: Setting) => {
  if (confirm('Are you sure you want to delete this setting?')) {
    try {
      await api.delete(`/admin/settings/${setting.id}`);
      showNotification('Setting deleted successfully', 'success');
      await fetchSettings();
    } catch (err) {
      showNotification('Failed to delete setting', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;

  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} settings?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} settings deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchSettings();
    } catch (err) {
      showNotification('Failed to delete settings', 'error');
    }
  }
};

// Utility methods
const truncateText = (text: string | undefined, limit: number) => {
  if (!text) return '-';
  return text.length > limit ? text.substring(0, limit) + '...' : text;
};

const isImage = (url: string) => {
  if (!url) return false;
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
};

const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(() => {
  fetchSettings();
});
</script>