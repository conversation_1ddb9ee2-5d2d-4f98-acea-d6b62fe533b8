import { useNotificationsStore } from '@/stores/notifications';

export function useNotifications() {
  const notificationsStore = useNotificationsStore();

  const showNotification = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info', title?: string) => {
    return notificationsStore.addNotification({
      type,
      message,
      title
    });
  };

  const showSuccess = (message: string, title?: string) => {
    return notificationsStore.success(message, title);
  };

  const showError = (message: string, title?: string) => {
    return notificationsStore.error(message, title);
  };

  const showInfo = (message: string, title?: string) => {
    return notificationsStore.info(message, title);
  };

  const showWarning = (message: string, title?: string) => {
    return notificationsStore.warning(message, title);
  };

  return {
    showNotification,
    showSuccess,
    showError,
    showInfo,
    showWarning
  };
}
