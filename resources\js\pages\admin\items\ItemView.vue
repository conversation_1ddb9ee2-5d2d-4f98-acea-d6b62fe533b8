<template>
  <AdminDetailTemplate
    :title="item?.name || 'Item Details'"
    :subtitle="`Reference: ${item?.reference_number || 'N/A'}`"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button
          variant="outline"
          @click="handleEdit"
          :disabled="!item"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Item
        </Button>
        <Button
          variant="outline"
          @click="handleDelete"
          :disabled="!item || item.closed_by"
          class="text-red-600 hover:text-red-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete Item
        </Button>
      </div>
    </template>

    <div v-if="item" class="space-y-6">
      <!-- Item Status -->
      <Card class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Status</h3>
            <p class="text-sm text-gray-500 mt-1">Current item status</p>
          </div>
          <AdminBadge
            :variant="item.closed_by ? 'success' : 'warning'"
            size="lg"
          >
            {{ item.closed_by ? 'Sold' : 'Available' }}
          </AdminBadge>
        </div>
      </Card>

      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Item Name</label>
            <p class="mt-1 text-sm text-gray-900">{{ item.name }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Reference Number</label>
            <p class="mt-1 text-sm text-gray-900">{{ item.reference_number || 'N/A' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Item Code</label>
            <p class="mt-1 text-sm text-gray-900">{{ item.code || 'N/A' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Target Amount</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatCurrency(item.target_amount) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Current Bid</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatCurrency(item.bid_amount) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Auction Type</label>
            <div class="mt-1">
              <AdminBadge
                v-if="item.auction_type"
                :variant="getAuctionTypeBadgeVariant(item.auction_type.type)"
                size="sm"
              >
                {{ item.auction_type.name }}
              </AdminBadge>
              <span v-else class="text-sm text-gray-400">No type assigned</span>
            </div>
          </div>

          <div v-if="item.description" class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Description</label>
            <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ item.description }}</p>
          </div>
        </div>
      </Card>

      <!-- Ownership & Location -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Ownership & Location</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Owner</label>
            <div v-if="item.user" class="mt-1">
              <p class="text-sm font-medium text-gray-900">{{ item.user.name }}</p>
              <p class="text-sm text-gray-500">{{ item.user.email }}</p>
            </div>
            <p v-else class="mt-1 text-sm text-gray-400">No owner assigned</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Branch</label>
            <p class="mt-1 text-sm text-gray-900">{{ item.branch?.name || 'N/A' }}</p>
          </div>
        </div>
      </Card>

      <!-- Dates -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Available From</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDate(item.date_from) || 'Not set' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Available Until</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDate(item.date_to) || 'Not set' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Created</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(item.created_at) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Last Updated</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(item.updated_at) }}</p>
          </div>
        </div>
      </Card>

      <!-- Images -->
      <Card v-if="item.media && item.media.length > 0" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Images</h3>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div
            v-for="(image, index) in item.media"
            :key="image.id"
            class="relative group cursor-pointer"
            @click="openImageModal(image, index)"
          >
            <img
              :src="image.url"
              :alt="`Image ${index + 1}`"
              class="w-full h-24 object-cover rounded-lg border hover:opacity-75 transition-opacity"
            />
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </Card>

      <!-- No Images State -->
      <Card v-else class="p-6">
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No images</h3>
          <p class="mt-1 text-sm text-gray-500">This item doesn't have any images yet.</p>
          <div class="mt-6">
            <Button variant="outline" @click="handleEdit">
              Add Images
            </Button>
          </div>
        </div>
      </Card>
    </div>
  </AdminDetailTemplate>

  <!-- Image Modal -->
  <AdminModal v-if="showImageModal && selectedImage" @close="closeImageModal" size="lg">
    <template #header>
      <h3 class="text-lg font-medium">{{ item?.name }} - Image {{ selectedImageIndex + 1 }}</h3>
    </template>
    
    <div class="space-y-4">
      <img
        :src="selectedImage.url"
        :alt="`Image ${selectedImageIndex + 1}`"
        class="w-full max-h-96 object-contain rounded-lg"
      />
      
      <!-- Image Navigation -->
      <div v-if="item?.media && item.media.length > 1" class="flex justify-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          @click="previousImage"
          :disabled="selectedImageIndex === 0"
        >
          Previous
        </Button>
        <span class="flex items-center text-sm text-gray-500">
          {{ selectedImageIndex + 1 }} of {{ item.media.length }}
        </span>
        <Button
          variant="outline"
          size="sm"
          @click="nextImage"
          :disabled="selectedImageIndex === item.media.length - 1"
        >
          Next
        </Button>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end space-x-3">
        <Button variant="outline" @click="closeImageModal">Close</Button>
        <Button variant="primary" @click="downloadImage">Download</Button>
      </div>
    </template>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminDetailTemplate } from '@/components/admin/templates';
import { AdminBadge, AdminModal } from '@/components/admin/ui';
import { Card, Button } from '@/components/ui';
import { useAdminItems } from '@/stores/admin/items';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Store
const itemsStore = useAdminItems();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const showImageModal = ref(false);
const selectedImage = ref<any>(null);
const selectedImageIndex = ref(0);

// Computed
const itemId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);
const item = computed(() => itemsStore.currentItem);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Items', href: '/admin-spa/items/list' },
  { label: item.value?.name || 'Item Details' }
]);

// Methods
const loadItem = async () => {
  if (!itemId.value) return;

  loading.value = true;
  error.value = null;

  try {
    await itemsStore.fetchItem(itemId.value);
  } catch (err) {
    error.value = 'Failed to load item';
    showNotification('Failed to load item', 'error');
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  if (itemId.value) {
    router.push(`/admin-spa/items/edit/${itemId.value}`);
  }
};

const handleDelete = async () => {
  if (!item.value) return;

  if (confirm(`Are you sure you want to delete "${item.value.name}"?`)) {
    try {
      await itemsStore.deleteItem(item.value.id);
      showNotification('Item deleted successfully', 'success');
      router.push('/admin-spa/items/list');
    } catch (error) {
      showNotification('Failed to delete item', 'error');
    }
  }
};

// Image modal methods
const openImageModal = (image: any, index: number) => {
  selectedImage.value = image;
  selectedImageIndex.value = index;
  showImageModal.value = true;
};

const closeImageModal = () => {
  showImageModal.value = false;
  selectedImage.value = null;
  selectedImageIndex.value = 0;
};

const previousImage = () => {
  if (selectedImageIndex.value > 0) {
    selectedImageIndex.value--;
    selectedImage.value = item.value?.media[selectedImageIndex.value];
  }
};

const nextImage = () => {
  if (item.value?.media && selectedImageIndex.value < item.value.media.length - 1) {
    selectedImageIndex.value++;
    selectedImage.value = item.value.media[selectedImageIndex.value];
  }
};

const downloadImage = () => {
  if (selectedImage.value?.url) {
    const link = document.createElement('a');
    link.href = selectedImage.value.url;
    link.download = `${item.value?.name || 'image'}-${selectedImageIndex.value + 1}`;
    link.click();
  }
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDate = (date: string | null | undefined) => {
  if (!date) return null;
  return new Date(date).toLocaleDateString();
};

const formatDateTime = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleString();
};

// Lifecycle
onMounted(() => {
  loadItem();
});
</script>
