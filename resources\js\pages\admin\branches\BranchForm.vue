<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-xl font-semibold text-gray-900">
          {{ isEditing ? 'Edit Branch' : 'Create Branch' }}
        </h1>
        <p class="mt-1 text-sm text-gray-600">
          {{ isEditing ? 'Update branch information' : 'Add a new branch location' }}
        </p>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">
            Branch Name *
          </label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            :class="{ 'border-red-300': errors.name }"
          />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">
            {{ errors.name }}
          </p>
        </div>

        <!-- Address -->
        <div>
          <label for="address" class="block text-sm font-medium text-gray-700">
            Address
          </label>
          <textarea
            id="address"
            v-model="form.address"
            rows="3"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            :class="{ 'border-red-300': errors.address }"
          />
          <p v-if="errors.address" class="mt-1 text-sm text-red-600">
            {{ errors.address }}
          </p>
        </div>

        <!-- Phone and Email Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Phone -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <input
              id="phone"
              v-model="form.phone"
              type="tel"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              :class="{ 'border-red-300': errors.phone }"
            />
            <p v-if="errors.phone" class="mt-1 text-sm text-red-600">
              {{ errors.phone }}
            </p>
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email Address
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              :class="{ 'border-red-300': errors.email }"
            />
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">
              {{ errors.email }}
            </p>
          </div>
        </div>

        <!-- Status -->
        <div>
          <label for="status_id" class="block text-sm font-medium text-gray-700">
            Status *
          </label>
          <select
            id="status_id"
            v-model="form.status_id"
            required
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            :class="{ 'border-red-300': errors.status_id }"
          >
            <option value="">Select Status</option>
            <option
              v-for="(name, id) in statuses"
              :key="id"
              :value="id"
            >
              {{ name }}
            </option>
          </select>
          <p v-if="errors.status_id" class="mt-1 text-sm text-red-600">
            {{ errors.status_id }}
          </p>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :loading="loading"
            :disabled="loading"
          >
            {{ isEditing ? 'Update Branch' : 'Create Branch' }}
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Button } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface BranchForm {
  name: string;
  address: string;
  phone: string;
  email: string;
  status_id: string | number;
}

// Router
const router = useRouter();
const route = useRoute();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const statuses = ref<Record<string, string>>({});
const errors = ref<Record<string, string>>({});

// Form data
const form = reactive<BranchForm>({
  name: '',
  address: '',
  phone: '',
  email: '',
  status_id: ''
});

// Computed
const isEditing = computed(() => !!route.params.id);
const branchId = computed(() => route.params.id as string);

// Methods
const fetchStatuses = async () => {
  try {
    const data = await api.get('/admin/branches/statuses');
    statuses.value = data;
  } catch (err) {
    console.error('Error fetching statuses:', err);
  }
};

const fetchBranch = async () => {
  if (!isEditing.value) return;

  loading.value = true;
  try {
    const data = await api.get(`/admin/branches/${branchId.value}`);
    Object.assign(form, {
      name: data.name || '',
      address: data.address || '',
      phone: data.phone || '',
      email: data.email || '',
      status_id: data.status_id || ''
    });
  } catch (err) {
    showNotification('Failed to fetch branch details', 'error');
    router.push('/admin-spa/branches/list');
  } finally {
    loading.value = false;
  }
};

const validateForm = () => {
  errors.value = {};

  if (!form.name.trim()) {
    errors.value.name = 'Branch name is required';
  }

  if (!form.status_id) {
    errors.value.status_id = 'Status is required';
  }

  if (form.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.value.email = 'Please enter a valid email address';
  }

  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  loading.value = true;
  try {
    const payload = { ...form };
    
    if (isEditing.value) {
      await api.put(`/admin/branches/${branchId.value}`, payload);
      showNotification('Branch updated successfully', 'success');
    } else {
      await api.post('/admin/branches', payload);
      showNotification('Branch created successfully', 'success');
    }

    router.push('/admin-spa/branches/list');
  } catch (err: any) {
    if (err.response?.data?.errors) {
      errors.value = err.response.data.errors;
    } else {
      showNotification(
        isEditing.value ? 'Failed to update branch' : 'Failed to create branch',
        'error'
      );
    }
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  router.push('/admin-spa/branches/list');
};

// Lifecycle
onMounted(async () => {
  await fetchStatuses();
  await fetchBranch();
});
</script>
