<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Laravel Backup Panel<?php echo e(config('app.name') ? ' - ' . config('app.name') : ''); ?></title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <link href="<?php echo e(asset('vendor/laravel_backup_panel/bootstrap.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(asset('vendor/laravel_backup_panel/app.css')); ?>" rel="stylesheet">
    <?php echo \Livewire\Livewire::styles(); ?>

</head>
<body>
    <?php
if (! isset($_instance)) {
    $html = \Livewire\Livewire::mount('laravel_backup_panel::app', [])->html();
} elseif ($_instance->childHasBeenRendered('4i3MVHI')) {
    $componentId = $_instance->getRenderedChildComponentId('4i3MVHI');
    $componentTag = $_instance->getRenderedChildComponentTagName('4i3MVHI');
    $html = \Livewire\Livewire::dummyMount($componentId, $componentTag);
    $_instance->preserveRenderedChild('4i3MVHI');
} else {
    $response = \Livewire\Livewire::mount('laravel_backup_panel::app', []);
    $html = $response->html();
    $_instance->logRenderedChild('4i3MVHI', $response->id(), \Livewire\Livewire::getRootElementTagName($html));
}
echo $html;
?>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ho+j7jyWK8fNQe+A12Hb8AhRq26LrZ/JpcUGGOn+Y7RsweNrtN/tE3MoK7ZeZDyx" crossorigin="anonymous"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <?php echo \Livewire\Livewire::scripts(); ?>

</body>
</html>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/vendor/laravel_backup_panel/layout.blade.php ENDPATH**/ ?>