<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Auth Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Admin Authentication Test</h1>
    
    <div>
        <button onclick="testSessionAuth()">Test Session Auth</button>
        <button onclick="testAdminAccess()">Test Admin Access</button>
        <button onclick="testAdminUsers()">Test Admin Users API</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testSessionAuth() {
            try {
                addResult('Testing session authentication...', 'info');
                
                const response = await fetch('/api/user-session', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const user = await response.json();
                    addResult(`✅ Session auth successful! User: ${user.name} (${user.email})`, 'success');
                    addResult(`<pre>${JSON.stringify(user, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Session auth failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Session auth error: ${error.message}`, 'error');
            }
        }

        async function testAdminAccess() {
            try {
                addResult('Testing admin access check...', 'info');
                
                const response = await fetch('/api/admin/check-access', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.hasAccess) {
                        addResult(`✅ Admin access granted! User: ${result.user.name}`, 'success');
                        addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                    } else {
                        addResult(`❌ Admin access denied: ${result.reason}`, 'error');
                    }
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Admin access check failed: ${response.status} ${response.statusText}`, 'error');
                    addResult(`Response: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Admin access error: ${error.message}`, 'error');
            }
        }

        async function testAdminUsers() {
            try {
                addResult('Testing admin users API...', 'info');
                
                const response = await fetch('/api/admin/users?page=1&perPage=5', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    addResult(`✅ Admin users API successful! Found ${result.data?.length || 0} users`, 'success');
                    addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Admin users API failed: ${response.status} ${response.statusText}`, 'error');
                    addResult(`Response: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Admin users API error: ${error.message}`, 'error');
            }
        }

        // Add CSRF token to page if available
        window.addEventListener('DOMContentLoaded', function() {
            // Try to get CSRF token from Laravel
            fetch('/sanctum/csrf-cookie', {
                credentials: 'include'
            }).then(() => {
                addResult('CSRF cookie initialized', 'info');
            }).catch(err => {
                addResult('Could not initialize CSRF cookie: ' + err.message, 'error');
            });
        });
    </script>
</body>
</html>
