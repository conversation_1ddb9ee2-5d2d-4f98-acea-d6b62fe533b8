<template>
  <AdminFormTemplate
    :title="isEditing ? 'Edit Auction Type' : 'Create Auction Type'"
    :subtitle="isEditing ? 'Update auction type details' : 'Add a new auction type'"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
    @submit="handleSubmit"
    @cancel="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Auction Type Name -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.name"
              label="Auction Type Name"
              placeholder="Enter auction type name"
              :error="errors.name"
              required
            />
          </div>

          <!-- Type -->
          <FormField
            v-model="form.type"
            label="Type"
            type="select"
            placeholder="Select type"
            :options="typeOptions"
            :error="errors.type"
            required
          />

          <!-- Status -->
          <FormField
            v-model="form.is_active"
            label="Status"
            type="select"
            placeholder="Select status"
            :options="statusOptions"
            :error="errors.is_active"
          />

          <!-- Description -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.description"
              label="Description"
              type="textarea"
              placeholder="Enter auction type description"
              :error="errors.description"
              rows="4"
            />
          </div>
        </div>
      </Card>

      <!-- Associated Items (for editing) -->
      <Card v-if="isEditing && associatedItems.length > 0" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Associated Items</h3>
        
        <div class="space-y-3">
          <div
            v-for="item in associatedItems"
            :key="item.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
          >
            <div class="flex items-center space-x-3">
              <img
                :src="item.image || '/img/product.jpeg'"
                :alt="item.name"
                class="w-10 h-10 rounded-lg object-cover"
              />
              <div>
                <p class="text-sm font-medium text-gray-900">{{ item.name }}</p>
                <p class="text-xs text-gray-500">Ref: {{ item.reference_number || 'N/A' }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <AdminBadge
                :variant="item.closed_by ? 'success' : 'warning'"
                size="sm"
              >
                {{ item.closed_by ? 'Sold' : 'Available' }}
              </AdminBadge>
              <Button
                variant="ghost"
                size="sm"
                @click="viewItem(item)"
                class="text-blue-600 hover:text-blue-700"
              >
                View
              </Button>
            </div>
          </div>
        </div>

        <div v-if="associatedItems.length > 5" class="mt-4 text-center">
          <Button
            variant="outline"
            size="sm"
            @click="showAllItems = !showAllItems"
          >
            {{ showAllItems ? 'Show Less' : `Show All ${totalAssociatedItems} Items` }}
          </Button>
        </div>
      </Card>

      <!-- Images/Media -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Images</h3>
        
        <AdminFileUpload
          v-model="form.media"
          accept="image/*"
          :multiple="true"
          :max-size="10"
          upload-text="Click to upload images or drag and drop"
          support-text="PNG, JPG, GIF up to 10MB each"
          @upload="handleImageUpload"
          @remove="handleImageRemove"
        />

        <!-- Existing Images (for editing) -->
        <div v-if="isEditing && existingImages.length > 0" class="mt-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Current Images</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="(image, index) in existingImages"
              :key="image.id"
              class="relative group"
            >
              <img
                :src="image.url"
                :alt="`Image ${index + 1}`"
                class="w-full h-24 object-cover rounded-lg border"
              />
              <Button
                variant="ghost"
                size="sm"
                @click="removeExistingImage(image.id)"
                class="absolute top-1 right-1 bg-red-500 text-white hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          @click="handleCancel"
          :disabled="submitting"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          {{ isEditing ? 'Update Auction Type' : 'Create Auction Type' }}
        </Button>
      </div>
    </form>
  </AdminFormTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminFormTemplate } from '@/components/admin/templates';
import { AdminBadge } from '@/components/admin/ui';
import { Card, Button, FormField } from '@/components/ui';
import { AdminFileUpload } from '@/components/admin/ui';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useAdminItems } from '@/stores/admin/items';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Stores
const auctionTypesStore = useAdminAuctionTypes();
const itemsStore = useAdminItems();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const submitting = ref(false);
const error = ref<string | null>(null);
const existingImages = ref<any[]>([]);
const associatedItems = ref<any[]>([]);
const totalAssociatedItems = ref(0);
const showAllItems = ref(false);

// Form data
const form = reactive({
  name: '',
  type: 'online' as 'online' | 'live' | 'cash',
  description: '',
  is_active: true,
  media: [] as File[]
});

// Form errors
const errors = reactive({
  name: '',
  type: '',
  description: '',
  is_active: '',
  media: ''
});

// Computed
const isEditing = computed(() => !!route.params.id);
const auctionTypeId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Auction Types', href: '/admin-spa/auction-types/list' },
  { label: isEditing.value ? 'Edit Auction Type' : 'Create Auction Type' }
]);

const isFormValid = computed(() => {
  return form.name.trim() !== '' && form.type !== '';
});

// Options for dropdowns
const typeOptions = computed(() => [
  { label: 'Online Auction', value: 'online' },
  { label: 'Live Auction', value: 'live' },
  { label: 'Cash Sale', value: 'cash' }
]);

const statusOptions = computed(() => [
  { label: 'Active', value: true },
  { label: 'Inactive', value: false }
]);

// Methods
const loadAuctionType = async () => {
  if (!auctionTypeId.value) return;

  loading.value = true;
  try {
    const auctionType = await auctionTypesStore.fetchAuctionType(auctionTypeId.value);
    
    // Populate form with auction type data
    form.name = auctionType.name || '';
    form.type = auctionType.type || 'online';
    form.description = auctionType.description || '';
    form.is_active = auctionType.is_active !== false;

    // Load existing images
    if (auctionType.media) {
      existingImages.value = auctionType.media;
    }

    // Load associated items
    await loadAssociatedItems();
  } catch (err) {
    error.value = 'Failed to load auction type';
    showNotification('Failed to load auction type', 'error');
  } finally {
    loading.value = false;
  }
};

const loadAssociatedItems = async () => {
  if (!auctionTypeId.value) return;

  try {
    // Fetch items associated with this auction type
    await itemsStore.fetchItems({
      auction_type_id: auctionTypeId.value.toString(),
      per_page: showAllItems.value ? undefined : 5
    });

    associatedItems.value = itemsStore.itemsList;
    totalAssociatedItems.value = itemsStore.totalItems;
  } catch (err) {
    console.error('Failed to load associated items:', err);
  }
};

const handleSubmit = async () => {
  if (!isFormValid.value) return;

  submitting.value = true;
  clearErrors();

  try {
    const auctionTypeData = {
      name: form.name,
      type: form.type,
      description: form.description || undefined,
      is_active: form.is_active,
      media: form.media.length > 0 ? form.media : undefined
    };

    if (isEditing.value && auctionTypeId.value) {
      await auctionTypesStore.updateAuctionType({ id: auctionTypeId.value, ...auctionTypeData });
      showNotification('Auction type updated successfully', 'success');
    } else {
      await auctionTypesStore.createAuctionType(auctionTypeData);
      showNotification('Auction type created successfully', 'success');
    }

    router.push('/admin-spa/auction-types/list');
  } catch (err: any) {
    if (err.response?.data?.errors) {
      Object.assign(errors, err.response.data.errors);
    } else {
      error.value = err.response?.data?.message || 'Failed to save auction type';
      showNotification('Failed to save auction type', 'error');
    }
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  router.push('/admin-spa/auction-types/list');
};

const handleImageUpload = (files: File[]) => {
  form.media = [...form.media, ...files];
};

const handleImageRemove = (index: number) => {
  form.media.splice(index, 1);
};

const removeExistingImage = async (imageId: number) => {
  if (confirm('Are you sure you want to remove this image?')) {
    try {
      // Call API to remove image
      await axios.delete(`/api/auction-types/${auctionTypeId.value}/media/${imageId}`);
      existingImages.value = existingImages.value.filter(img => img.id !== imageId);
      showNotification('Image removed successfully', 'success');
    } catch (err) {
      showNotification('Failed to remove image', 'error');
    }
  }
};

const viewItem = (item: any) => {
  router.push(`/admin-spa/items/view/${item.id}`);
};

const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
  error.value = null;
};

// Lifecycle
onMounted(async () => {
  if (isEditing.value) {
    await loadAuctionType();
  }
});
</script>
