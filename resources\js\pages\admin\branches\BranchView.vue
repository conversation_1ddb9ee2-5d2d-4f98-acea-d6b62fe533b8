<template>
  <div class="max-w-4xl mx-auto p-6">
    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="branch" class="bg-white shadow rounded-lg">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">
              {{ branch.name }}
            </h1>
            <p class="mt-1 text-sm text-gray-600">
              Branch Details
            </p>
          </div>
          <div class="flex space-x-3">
            <Button
              variant="outline"
              @click="handleEdit"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </Button>
            <Button
              variant="outline"
              @click="handleBack"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to List
            </Button>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Basic Information -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ branch.name || '-' }}</dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1">
                <Badge 
                  :variant="branch.status?.id === 1 ? 'success' : 'secondary'"
                >
                  {{ branch.status?.name || 'Unknown' }}
                </Badge>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Address</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <div v-if="branch.address" class="whitespace-pre-line">
                  {{ branch.address }}
                </div>
                <span v-else class="text-gray-400">-</span>
              </dd>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Phone</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <a 
                  v-if="branch.phone" 
                  :href="`tel:${branch.phone}`"
                  class="text-blue-600 hover:text-blue-800"
                >
                  {{ branch.phone }}
                </a>
                <span v-else class="text-gray-400">-</span>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Email</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <a 
                  v-if="branch.email" 
                  :href="`mailto:${branch.email}`"
                  class="text-blue-600 hover:text-blue-800"
                >
                  {{ branch.email }}
                </a>
                <span v-else class="text-gray-400">-</span>
              </dd>
            </div>
          </div>
        </div>

        <!-- Metadata -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500">Created By</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ branch.created_by_user?.name || 'Unknown' }}
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Updated By</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ branch.updated_by_user?.name || 'Unknown' }}
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Created At</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ formatDate(branch.created_at) }}
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Updated At</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ formatDate(branch.updated_at) }}
              </dd>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface Branch {
  id: number;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  status_id: number;
  status?: {
    id: number;
    name: string;
  };
  created_by_user?: {
    name: string;
  };
  updated_by_user?: {
    name: string;
  };
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();
const route = useRoute();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const branch = ref<Branch | null>(null);

// Computed
const branchId = computed(() => route.params.id as string);

// Methods
const fetchBranch = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    const data = await api.get(`/admin/branches/${branchId.value}`);
    branch.value = data;
  } catch (err) {
    error.value = 'Failed to fetch branch details';
    console.error('Error fetching branch:', err);
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  router.push(`/admin-spa/branches/edit/${branchId.value}`);
};

const handleBack = () => {
  router.push('/admin-spa/branches/list');
};

const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
  fetchBranch();
});
</script>
