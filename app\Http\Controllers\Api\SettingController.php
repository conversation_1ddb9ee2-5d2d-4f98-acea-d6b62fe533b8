<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\ActiveConfig;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\SettingResource;
use App\Http\Resources\SettingCollection;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', User::class);

        $search = $request->get('search', '');

        $settings = ActiveConfig::search($search)
            ->with(['createdBy', 'user'])
            ->latest()
            ->whereNull("user_id")
            ->paginate();

        return new SettingCollection($settings);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize('create', User::class);

        $validated = $request->validate([
            'key' => 'required|string|max:255',
            'value' => 'required|string',
        ]);

        $validated['key'] = _keyText($validated['key']);

        $setting = ActiveConfig::updateOrCreate(
            ['key' => $validated['key']],
            $validated
        );

        if ($request->hasFile('media')) {
            $setting->clearMediaCollection('media');
            $setting->addMediaFromRequest('media')->toMediaCollection('media');
        }

        return new SettingResource($setting->load(['createdBy', 'user']));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ActiveConfig  $setting
     * @return \Illuminate\Http\Response
     */
    public function show(ActiveConfig $setting)
    {
        $this->authorize('view', User::class);

        return new SettingResource($setting->load(['createdBy', 'user']));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ActiveConfig  $setting
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ActiveConfig $setting)
    {
        $this->authorize('update', User::class);

        $validated = $request->validate([
            'key' => 'required|string|max:255',
            'value' => 'required|string',
        ]);

        $validated['key'] = _keyText($validated['key']);

        $setting->update($validated);

        if ($request->hasFile('media')) {
            $setting->clearMediaCollection('media');
            $setting->addMediaFromRequest('media')->toMediaCollection('media');
        }

        return new SettingResource($setting->load(['createdBy', 'user']));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ActiveConfig  $setting
     * @return \Illuminate\Http\Response
     */
    public function destroy(ActiveConfig $setting)
    {
        $this->authorize('delete', User::class);

        $setting->clearMediaCollection('media');
        $setting->delete();

        return response()->noContent();
    }
}
