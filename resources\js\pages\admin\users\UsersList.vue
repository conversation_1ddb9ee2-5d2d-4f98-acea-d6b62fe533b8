<template>
  <AdminListTemplate
    title="All Users"
    subtitle="View and manage all users"
    :loading="loading"
    :error="error"
    :items="users"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add User"
    empty-state-title="No users found"
    empty-state-message="Start by creating a new user account."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchUsers"
  >
    <template #rows="{ items }">
      <tr
        v-for="user in items"
        :key="user.id"
        class="hover:bg-gray-50"
      >
        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                <span class="text-sm font-medium text-gray-700">
                  {{ getInitials(user.name) }}
                </span>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">
                <a
                  :href="`/admin-spa/users/view/${user.id}`"
                  class="hover:text-blue-600"
                >
                  {{ user.name || '-' }}
                </a>
              </div>
              <div class="text-sm text-gray-500">
                {{ user.email || '-' }}
              </div>
            </div>
          </div>
        </td>

        <!-- Phone -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ user.phone || '-' }}
        </td>

        <!-- Roles -->
        <td class="px-6 py-4">
          <div class="flex flex-wrap gap-1">
            <Badge
              v-for="role in user.roles"
              :key="role.id"
              variant="secondary"
              size="sm"
            >
              {{ role.name }}
            </Badge>
            <span v-if="!user.roles?.length" class="text-gray-400 text-sm">No roles</span>
          </div>
        </td>

        <!-- Status -->
        <td class="px-6 py-4 whitespace-nowrap">
          <Badge
            :variant="user.email_verified_at ? 'success' : 'warning'"
          >
            {{ user.email_verified_at ? 'Verified' : 'Unverified' }}
          </Badge>
        </td>

        <!-- Last Login -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ user.last_login_at ? formatDate(user.last_login_at) : 'Never' }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(user)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(user)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(user)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  email_verified_at?: string;
  last_login_at?: string;
  roles?: Array<{
    id: number;
    name: string;
  }>;
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const users = ref<User[]>([]);

// Filters
const filters = ref({
  search: ''
});

// Table columns
const columns = [
  { key: 'name', label: 'User', sortable: true },
  { key: 'phone', label: 'Phone', sortable: false },
  { key: 'roles', label: 'Roles', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'last_login_at', label: 'Last Login', sortable: true }
];

// Methods
const fetchUsers = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/admin/users', params);
    users.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch users';
    console.error('Error fetching users:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchUsers();
};

const handleCreate = () => {
  router.push('/admin-spa/users/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchUsers();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? users.value.map(u => u.id.toString()) : [];
};

const handleEdit = (user: User) => {
  router.push(`/admin-spa/users/edit/${user.id}`);
};

const handleView = (user: User) => {
  router.push(`/admin-spa/users/view/${user.id}`);
};

const handleDelete = async (user: User) => {
  if (confirm('Are you sure you want to delete this user?')) {
    try {
      await api.delete(`/admin/users/${user.id}`);
      showNotification('User deleted successfully', 'success');
      await fetchUsers();
    } catch (err) {
      showNotification('Failed to delete user', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;

  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} users?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} users deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchUsers();
    } catch (err) {
      showNotification('Failed to delete users', 'error');
    }
  }
};

// Utility methods
const getInitials = (name: string) => {
  if (!name) return '?';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
};

const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(() => {
  fetchUsers();
});
</script>