<template>
  <AdminListTemplate
    title="Auction Listings"
    subtitle="Manage auction listings and bidding sessions"
    :loading="loading"
    :error="error"
    :items="auctions"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Create Auction"
    empty-state-title="No auction listings found"
    empty-state-message="Get started by creating your first auction listing."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @view="handleView"
    @edit="handleEdit"
    @delete="handleDelete"
    @refresh="fetchAuctions"
  >
    <template #filters>
      <Select
        v-model="filters.status"
        placeholder="All Status"
        :options="statusOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.auction_type_id"
        placeholder="All Types"
        :options="auctionTypeOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.branch_id"
        placeholder="All Branches"
        :options="branchOptions"
        @change="applyFilters"
      />
      <FormField
        v-model="filters.date_from"
        type="date"
        placeholder="From Date"
        @change="applyFilters"
      />
      <FormField
        v-model="filters.date_to"
        type="date"
        placeholder="To Date"
        @change="applyFilters"
      />
    </template>

    <!-- Custom cell templates -->
    <template #cell-item="{ item }">
      <div class="flex items-center space-x-3">
        <img
          :src="item.item?.image || '/img/product.jpeg'"
          :alt="item.item?.name || 'No item'"
          class="w-12 h-12 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity"
          @click="viewItem(item.item)"
        />
        <div class="min-w-0 flex-1">
          <p class="text-sm font-medium text-gray-900 truncate">
            {{ item.item?.name || 'No item assigned' }}
          </p>
          <p class="text-sm text-gray-500 truncate">
            Ref: {{ item.item?.reference_number || 'N/A' }}
          </p>
          <p class="text-xs text-gray-400 truncate">
            Code: {{ item.code || 'N/A' }}
          </p>
        </div>
      </div>
    </template>

    <template #cell-auction_details="{ item }">
      <div class="min-w-0">
        <p class="text-sm font-medium text-gray-900 truncate">
          {{ item.name || 'Unnamed Auction' }}
        </p>
        <div class="flex items-center space-x-2 mt-1">
          <AdminBadge
            v-if="item.auctionType"
            :variant="getAuctionTypeBadgeVariant(item.auctionType.type)"
            size="sm"
          >
            {{ item.auctionType.name }}
          </AdminBadge>
        </div>
        <p v-if="item.description" class="text-xs text-gray-500 truncate mt-1">
          {{ item.description }}
        </p>
      </div>
    </template>

    <template #cell-bidding="{ item }">
      <div class="text-sm">
        <p class="font-medium text-gray-900">
          Current: {{ formatCurrency(item.bid_amount) }}
        </p>
        <p class="text-gray-600">
          Target: {{ formatCurrency(item.item?.target_amount) }}
        </p>
        <p v-if="item.initial_payment" class="text-green-600 text-xs">
          Paid: {{ formatCurrency(item.initial_payment) }}
        </p>
      </div>
    </template>

    <template #cell-dates="{ item }">
      <div class="text-xs text-gray-500">
        <p v-if="item.date_from">
          <span class="font-medium">From:</span> {{ formatDateTime(item.date_from) }}
        </p>
        <p v-if="item.date_to">
          <span class="font-medium">To:</span> {{ formatDateTime(item.date_to) }}
        </p>
        <p v-if="!item.date_from && !item.date_to" class="text-gray-400">
          No dates set
        </p>
      </div>
    </template>

    <template #cell-status="{ item }">
      <AdminBadge
        :variant="item.closed_by ? 'success' : 'warning'"
        size="sm"
      >
        {{ item.closed_by ? 'Closed' : 'Active' }}
      </AdminBadge>
    </template>

    <template #cell-owner="{ item }">
      <div v-if="item.user" class="text-sm">
        <p class="font-medium text-gray-900">{{ item.user.name }}</p>
        <p class="text-gray-500">{{ item.user.email }}</p>
      </div>
      <span v-else class="text-sm text-gray-400">No owner</span>
    </template>

    <template #actions="{ item }">
      <div class="flex justify-end gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="handleView(item)"
          class="text-blue-600 hover:text-blue-700"
        >
          View
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="handleEdit(item)"
          class="text-green-600 hover:text-green-700"
        >
          Edit
        </Button>
        <Button
          v-if="!item.closed_by"
          variant="ghost"
          size="sm"
          @click="handleClose(item)"
          class="text-orange-600 hover:text-orange-700"
        >
          Close
        </Button>
        <Button
          v-else
          variant="ghost"
          size="sm"
          @click="handleReopen(item)"
          class="text-blue-600 hover:text-blue-700"
        >
          Reopen
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="handleDelete(item)"
          class="text-red-600 hover:text-red-700"
        >
          Delete
        </Button>
      </div>
    </template>

    <template #bulk-actions="{ selectedItems }">
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkClose"
        class="text-orange-600 hover:text-orange-700"
      >
        Close Selected
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkReopen"
        class="text-blue-600 hover:text-blue-700"
      >
        Reopen Selected
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkDelete"
        class="text-red-600 hover:text-red-700"
      >
        Delete Selected
      </Button>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminBadge } from '@/components/admin/ui';
import { Button, Select, FormField } from '@/components/ui';
import { useAdminAuctions } from '@/stores/admin/auctions';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useAdminBranches } from '@/stores/admin/branches';
import { useNotifications } from '@/composables/useNotifications';

// Stores
const auctionsStore = useAdminAuctions();
const auctionTypesStore = useAdminAuctionTypes();
const branchesStore = useAdminBranches();

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const selectedItems = ref<string[]>([]);

// Filters
const filters = reactive({
  status: '',
  auction_type_id: '',
  branch_id: '',
  date_from: '',
  date_to: '',
  search: ''
});

// Computed
const auctions = computed(() => auctionsStore.auctionsList || []);
const loading = computed(() => auctionsStore.loading);
const error = computed(() => auctionsStore.error);
const currentPage = computed(() => auctionsStore.currentPage);
const totalPages = computed(() => auctionsStore.lastPage);
const totalItems = computed(() => auctionsStore.totalAuctions);
const perPage = computed(() => auctionsStore.auctions?.per_page || 20);

// Table columns configuration
const columns = computed(() => [
  { key: 'item', label: 'Item', sortable: false },
  { key: 'auction_details', label: 'Auction Details', sortable: true },
  { key: 'bidding', label: 'Bidding Info', sortable: false },
  { key: 'dates', label: 'Schedule', sortable: true },
  { key: 'status', label: 'Status', sortable: false },
  { key: 'owner', label: 'Owner', sortable: false }
]);

// Filter options
const statusOptions = computed(() => [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Closed', value: 'closed' }
]);

const auctionTypeOptions = computed(() => [
  { label: 'All Types', value: '' },
  ...auctionTypesStore.auctionTypes.map(type => ({
    label: type.name,
    value: type.id.toString()
  }))
]);

const branchOptions = computed(() => [
  { label: 'All Branches', value: '' },
  ...branchesStore.branches.map(branch => ({
    label: branch.name,
    value: branch.id.toString()
  }))
]);

// Methods
const fetchAuctions = async () => {
  await auctionsStore.fetchAuctions({
    ...filters,
    page: currentPage.value,
    per_page: perPage.value
  });
};

const applyFilters = () => {
  fetchAuctions();
};

const handleCreate = () => {
  router.push('/admin-spa/auction-listings/create');
};

const handleView = (auction: any) => {
  router.push(`/admin-spa/auction-listings/view/${auction.id}`);
};

const handleEdit = (auction: any) => {
  router.push(`/admin-spa/auction-listings/edit/${auction.id}`);
};

const handleDelete = async (auction: any) => {
  if (confirm(`Are you sure you want to delete this auction listing?`)) {
    try {
      await auctionsStore.deleteAuction(auction.id);
      showNotification('Auction listing deleted successfully', 'success');
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to delete auction listing', 'error');
    }
  }
};

const handleClose = async (auction: any) => {
  if (confirm(`Are you sure you want to close this auction?`)) {
    try {
      await auctionsStore.closeAuction(auction.id);
      showNotification('Auction closed successfully', 'success');
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to close auction', 'error');
    }
  }
};

const handleReopen = async (auction: any) => {
  if (confirm(`Are you sure you want to reopen this auction?`)) {
    try {
      await auctionsStore.reopenAuction(auction.id);
      showNotification('Auction reopened successfully', 'success');
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to reopen auction', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} auction listings?`)) {
    try {
      await auctionsStore.bulkDeleteAuctions(selectedItems.value);
      showNotification(`${selectedItems.value.length} auction listings deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to delete auction listings', 'error');
    }
  }
};

const handleBulkClose = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to close ${selectedItems.value.length} auctions?`)) {
    try {
      // Implement bulk close functionality
      showNotification(`${selectedItems.value.length} auctions closed successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to close auctions', 'error');
    }
  }
};

const handleBulkReopen = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to reopen ${selectedItems.value.length} auctions?`)) {
    try {
      // Implement bulk reopen functionality
      showNotification(`${selectedItems.value.length} auctions reopened successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctions();
    } catch (error) {
      showNotification('Failed to reopen auctions', 'error');
    }
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  fetchAuctions();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic
  fetchAuctions();
};

const handlePageChange = (page: number) => {
  fetchAuctions();
};

const handleSelectAll = (selected: boolean) => {
  if (selected) {
    selectedItems.value = auctions.value.map(auction => auction.id.toString());
  } else {
    selectedItems.value = [];
  }
};

const viewItem = (item: any) => {
  if (item?.id) {
    router.push(`/admin-spa/items/view/${item.id}`);
  }
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDateTime = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleString();
};

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchAuctions(),
    auctionTypesStore.fetchAuctionTypes(),
    branchesStore.fetchBranches()
  ]);
});
</script>
