<template>
  <AdminListTemplate
    title="Suppliers"
    subtitle="Manage suppliers and vendors"
    :loading="loading"
    :error="error"
    :items="suppliers"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Supplier"
    empty-state-title="No suppliers found"
    empty-state-message="Get started by adding your first supplier."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchSuppliers"
  >
    <template #rows="{ items }">
      <tr
        v-for="(supplier, index) in items"
        :key="supplier.id"
        class="hover:bg-gray-50"
      >
        <!-- # Column -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ (currentPage - 1) * perPage + index + 1 }}
        </td>

        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <img
                class="h-10 w-10 rounded-full"
                :src="supplier.avatar || '/images/default-avatar.png'"
                :alt="supplier.name"
              />
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">
                <a
                  :href="`/suppliers/${supplier.id}`"
                  class="hover:text-blue-600"
                >
                  {{ supplier.name }}
                </a>
              </div>
              <div v-if="supplier.phone" class="text-sm text-gray-500">
                {{ supplier.phone }}
              </div>
            </div>
          </div>
        </td>

        <!-- Email Column -->
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900">{{ supplier.email }}</div>
        </td>

        <!-- Items Column -->
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <Badge variant="primary">
            {{ getItemsCount(supplier) }}
          </Badge>
        </td>

        <!-- Branch Column -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ supplier.branch?.name || '-' }}
        </td>

        <!-- Actions Column -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(supplier)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(supplier)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(supplier)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';
import type { User } from '@/types';

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const suppliers = ref<User[]>([]);

// Filters
const filters = reactive({
  search: ''
});

// Table columns
const columns = [
  { key: 'index', label: '#', sortable: false },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email Address', sortable: true },
  { key: 'items_count', label: 'Items', sortable: false },
  { key: 'branch', label: 'Branch', sortable: true }
];

// Methods
const fetchSuppliers = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters
    };

    const data = await api.get('/suppliers', params);
    suppliers.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch suppliers';
    console.error('Error fetching suppliers:', err);
  } finally {
    loading.value = false;
  }
};

const handleCreate = () => {
  router.push('/admin-spa/suppliers/create');
};

const handleSearch = (query: string) => {
  filters.search = query;
  currentPage.value = 1;
  fetchSuppliers();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchSuppliers();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? suppliers.value.map(s => s.id.toString()) : [];
};

const handleEdit = (supplier: User) => {
  router.push(`/admin-spa/suppliers/edit/${supplier.id}`);
};

const handleView = (supplier: User) => {
  router.push(`/admin-spa/suppliers/view/${supplier.id}`);
};

const handleDelete = async (supplier: User) => {
  if (confirm('Are you sure you want to delete this supplier?')) {
    try {
      await api.delete(`/users/${supplier.id}`);
      showNotification('Supplier deleted successfully', 'success');
      await fetchSuppliers();
    } catch (err) {
      showNotification('Failed to delete supplier', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} suppliers?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} suppliers deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchSuppliers();
    } catch (err) {
      showNotification('Failed to delete suppliers', 'error');
    }
  }
};

// Utility methods
const getItemsCount = (supplier: User) => {
  return supplier.items?.filter(item => !item.closed_by).length || 0;
};

// Lifecycle
onMounted(() => {
  fetchSuppliers();
});
</script>
