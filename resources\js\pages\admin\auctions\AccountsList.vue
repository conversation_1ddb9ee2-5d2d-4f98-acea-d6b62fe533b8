<template>
  <AdminListTemplate
    title="Accounts"
    subtitle="Manage user accounts and balances"
    :loading="loading"
    :error="error"
    :items="accounts"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Account"
    empty-state-title="No accounts found"
    empty-state-message="Get started by adding your first account."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchAccounts"
  >
    <template #rows="{ items }">
      <tr
        v-for="(account, index) in items"
        :key="account.id"
        class="hover:bg-gray-50"
      >
        <!-- # Column -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ (currentPage - 1) * perPage + index + 1 }}
        </td>

        <!-- Customer Name Column -->
        <td class="px-6 py-4">
          <div class="text-sm font-medium text-gray-900">
            <a
              :href="`/users/${account.user_id}`"
              class="hover:text-blue-600"
            >
              {{ account.user?.name || '-' }}
            </a>
          </div>
        </td>

        <!-- Account Name Column -->
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900">{{ account.account_name || '-' }}</div>
        </td>

        <!-- Amount Column -->
        <td class="px-6 py-4 whitespace-nowrap text-right">
          <div class="text-sm font-medium text-gray-900">
            {{ formatCurrency(account.amount) }}
          </div>
        </td>

        <!-- Account Number Column -->
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-gray-900">{{ account.account_number || '-' }}</div>
        </td>

        <!-- Account Type Column -->
        <td class="px-6 py-4 whitespace-nowrap">
          <Badge :variant="getAccountTypeVariant(account.account_type)">
            {{ account.account_type || '-' }}
          </Badge>
        </td>

        <!-- Actions Column -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(account)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              @click="handleView(account)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              @click="handleDelete(account)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';
import type { Account } from '@/types';

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const accounts = ref<Account[]>([]);

// Filters
const filters = reactive({
  search: ''
});

// Table columns
const columns = [
  { key: 'index', label: '#', sortable: false },
  { key: 'customer_name', label: 'Customer Name', sortable: true },
  { key: 'account_name', label: 'Account Name', sortable: true },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'account_number', label: 'Account Number', sortable: true },
  { key: 'account_type', label: 'Account Type', sortable: true }
];

// Methods
const fetchAccounts = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters
    };

    const data = await api.get('/accounts', params);
    // accounts.value = data.data;
    // totalItems.value = data.meta?.total || 0;
    // totalPages.value = data.meta?.last_page || 1;
    // currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch accounts';
    console.error('Error fetching accounts:', err);
  } finally {
    loading.value = false;
  }
};

const handleCreate = () => {
  router.push('/admin-spa/accounts/create');
};

const handleSearch = (query: string) => {
  filters.search = query;
  currentPage.value = 1;
  fetchAccounts();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAccounts();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? accounts.value.map(a => a.id.toString()) : [];
};

const handleEdit = (account: Account) => {
  router.push(`/admin-spa/accounts/edit/${account.id}`);
};

const handleView = (account: Account) => {
  router.push(`/admin-spa/accounts/view/${account.id}`);
};

const handleDelete = async (account: Account) => {
  if (confirm('Are you sure you want to delete this account?')) {
    try {
      await api.delete(`/accounts/${account.id}`);
      showNotification('Account deleted successfully', 'success');
      await fetchAccounts();
    } catch (err) {
      showNotification('Failed to delete account', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} accounts?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} accounts deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchAccounts();
    } catch (err) {
      showNotification('Failed to delete accounts', 'error');
    }
  }
};

// Utility methods
const formatCurrency = (amount: number | string | null) => {
  if (!amount) return '-';
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(num);
};

const getAccountTypeVariant = (type: string | null) => {
  if (!type) return 'default';
  
  switch (type.toLowerCase()) {
    case 'savings': return 'success';
    case 'checking': return 'primary';
    case 'credit': return 'warning';
    case 'loan': return 'danger';
    default: return 'default';
  }
};

// Lifecycle
onMounted(() => {
  fetchAccounts();
});
</script>
