<template>
  <AdminListTemplate
    title="All Branches"
    subtitle="View and manage all branches"
    :loading="loading"
    :error="error"
    :items="branches"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Branch"
    empty-state-title="No branches found"
    empty-state-message="Start by creating a new branch location."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchBranches"
  >
    <template #rows="{ items }">
      <tr
        v-for="branch in items"
        :key="branch.id"
        class="hover:bg-gray-50"
      >
        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="text-sm font-medium text-gray-900">
            <a
              :href="`/admin-spa/branches/view/${branch.id}`"
              class="hover:text-blue-600"
            >
              {{ branch.name || '-' }}
            </a>
          </div>
        </td>

        <!-- Phone -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ branch.phone || '-' }}
        </td>

        <!-- Email -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ branch.email || '-' }}
        </td>

        <!-- Status -->
        <td class="px-6 py-4 whitespace-nowrap">
          <Badge 
            :variant="branch.status?.id === 1 ? 'success' : 'secondary'"
          >
            {{ branch.status?.name || 'Unknown' }}
          </Badge>
        </td>

        <!-- Address -->
        <td class="px-6 py-4 text-sm text-gray-900">
          <span
            class="truncate block max-w-xs"
            :title="branch.address"
          >
            {{ truncateText(branch.address, 50) }}
          </span>
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(branch)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(branch)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(branch)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface Branch {
  id: number;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  status_id: number;
  status?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const branches = ref<Branch[]>([]);

// Filters
const filters = ref({
  search: ''
});

// Table columns
const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'phone', label: 'Phone', sortable: false },
  { key: 'email', label: 'Email', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'address', label: 'Address', sortable: false }
];

// Methods
const fetchBranches = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/admin/branches', params);
    branches.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch branches';
    console.error('Error fetching branches:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchBranches();
};

const handleCreate = () => {
  router.push('/admin-spa/branches/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchBranches();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? branches.value.map(b => b.id.toString()) : [];
};

const handleEdit = (branch: Branch) => {
  router.push(`/admin-spa/branches/edit/${branch.id}`);
};

const handleView = (branch: Branch) => {
  router.push(`/admin-spa/branches/view/${branch.id}`);
};

const handleDelete = async (branch: Branch) => {
  if (confirm('Are you sure you want to delete this branch?')) {
    try {
      await api.delete(`/admin/branches/${branch.id}`);
      showNotification('Branch deleted successfully', 'success');
      await fetchBranches();
    } catch (err) {
      showNotification('Failed to delete branch', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} branches?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} branches deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchBranches();
    } catch (err) {
      showNotification('Failed to delete branches', 'error');
    }
  }
};

// Utility methods
const truncateText = (text: string | undefined, limit: number) => {
  if (!text) return '-';
  return text.length > limit ? text.substring(0, limit) + '...' : text;
};

// Lifecycle
onMounted(() => {
  fetchBranches();
});
</script>
