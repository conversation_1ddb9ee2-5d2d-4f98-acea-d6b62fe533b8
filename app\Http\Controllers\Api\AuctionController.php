<?php

namespace App\Http\Controllers\Api;

use App\Models\Item;
use App\Models\Auction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\AuctionCollection;
use App\Http\Requests\AuctionStoreRequest;
use App\Http\Requests\AuctionUpdateRequest;

use Facades\App\Cache\Repo;
use Facades\App\Libraries\AuctionHandler;


class AuctionController extends Controller
{

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function liveAuctions(Request $request)
    {

        $auctions = Repo::liveAuction();
        return new AuctionCollection($auctions);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function onlineAuctions(Request $request)
    {

        $auctions = Repo::onlineAuction();
        return new AuctionCollection($auctions);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Auction::class);

        // Build the query with relationships
        $query = Auction::with(['auctionType', 'item', 'user'])
            ->whereHas('item')
            ->latest();

        // Apply search filter
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Apply item filter
        if ($request->filled('item_id') && $request->item_id != '0') {
            $query->where('item_id', $request->item_id);
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'open') {
                $query->whereNull('closed_by');
            } elseif ($request->status === 'closed') {
                $query->whereNotNull('closed_by');
            }
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 20);
        $page = $request->get('page', 1);

        // Paginate the results
        $auctions = $query->paginate($perPage, ['*'], 'page', $page);

        return new AuctionCollection($auctions);
    }

    /**
     * @param \App\Http\Requests\AuctionStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(AuctionStoreRequest $request)
    {
        $this->authorize('create', Auction::class);

        $validated = $request->validated();

        $auction = Auction::create($validated);

        return new AuctionResource($auction);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Auction $auction)
    {
        $this->authorize('view', $auction);

        return new AuctionResource($auction);
    }

    /**
     * @param \App\Http\Requests\AuctionUpdateRequest $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function update(AuctionUpdateRequest $request, Auction $auction)
    {
        $this->authorize('update', $auction);

        $validated = $request->validated();

        $auction->update($validated);

        return new AuctionResource($auction);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Auction $auction
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Auction $auction)
    {
        $this->authorize('delete', $auction);

        $auction->delete();

        return response()->noContent();
    }


    public function updateAuction(Request $request, $code) { 

        $auction = Auction::where("code", $code)->first();
        
        if( $auction) {
            $auction->update($request->all());
        }

        return response([
            "data" => $auction,
        ]);
    }


    public function placeABid(Request $request, Item $item, $amount) {
        $data = AuctionHandler::placeABid($item, $amount);
        return response($data);
    }

    /**
     * Check if user is subscribed to a specific auction type
     */
    public function checkSubscription(Request $request, $auctionTypeId) {
        $user = auth()->user();

        if (!$user) {
            return response()->json([
                'subscribed' => false,
                'message' => 'User not authenticated'
            ], 401);
        }

        // Check if user has an active auction for this auction type
        $auction = \App\Models\Auction::where('auction_type_id', $auctionTypeId)
                    ->where('user_id', $user->id)
                    ->whereNull('closed_by')
                    ->first();

        $auctionType = \App\Models\AuctionType::find($auctionTypeId);

        return response()->json([
            'subscribed' => !is_null($auction),
            'auction_type' => $auctionType ? $auctionType->name : null,
            'auction_type_type' => $auctionType ? $auctionType->type : null,
            'message' => $auction ? 'User is subscribed' : 'User is not subscribed'
        ]);
    }




}
