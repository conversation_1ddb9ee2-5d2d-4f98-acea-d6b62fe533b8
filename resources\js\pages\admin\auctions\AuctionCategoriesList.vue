<template>
  <AdminListTemplate
    title="Auction Categories"
    subtitle="Manage auction categories and types"
    :loading="loading"
    :error="error"
    :items="auctionTypes"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Auction Category"
    empty-state-title="No auction categories found"
    empty-state-message="Get started by adding your first auction category."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchAuctionTypes"
  >
    <template #filters>
      <Select
        v-model="filters.type"
        placeholder="All Types"
        :options="typeOptions"
        @change="applyFilters"
      />
    </template>

    <template #rows="{ items }">
      <tr
        v-for="auctionType in items"
        :key="auctionType.id"
        class="hover:bg-gray-50"
      >
        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              <a
                :href="`/auction-types/${auctionType.id}`"
                class="hover:text-blue-600"
              >
                {{ auctionType.name }}
              </a>
            </p>
            <p v-if="auctionType.description" class="text-sm text-gray-500 truncate">
              {{ auctionType.description }}
            </p>
          </div>
        </td>

        <!-- Type Column -->
        <td class="px-6 py-4 whitespace-nowrap">
          <Badge :variant="getTypeVariant(auctionType.type)">
            {{ formatType(auctionType.type) }}
          </Badge>
        </td>

        <!-- Available Items -->
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <Badge variant="primary">
            {{ getAvailableItemsCount(auctionType) }}
          </Badge>
        </td>

        <!-- Created By -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ auctionType.created_by?.name || '-' }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(auctionType)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(auctionType)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(auctionType)"
              :disabled="getAvailableItemsCount(auctionType) > 0"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge, Select } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';
import type { AuctionType } from '@/types';

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const auctionTypes = ref<AuctionType[]>([]);

// Filters
const filters = reactive({
  type: '',
  search: ''
});

// Table columns
const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'type', label: 'Type', sortable: true },
  { key: 'items_count', label: 'Available Items', sortable: false },
  { key: 'created_by', label: 'Created By', sortable: true }
];

// Computed
const typeOptions = computed(() => [
  { label: 'All Types', value: '' },
  { label: 'Online', value: 'online' },
  { label: 'Cash', value: 'cash' }
]);



// Methods
const fetchAuctionTypes = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters
    };

    const data = await api.get('/auction-types', params);
    auctionTypes.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch auction categories';
    console.error('Error fetching auction categories:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchAuctionTypes();
};

const handleCreate = () => {
  router.push('/admin-spa/auction-types/create');
};

const handleSearch = (query: string) => {
  filters.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAuctionTypes();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? auctionTypes.value.map(a => a.id.toString()) : [];
};

const handleEdit = (auctionType: AuctionType) => {
  router.push(`/admin-spa/auction-types/edit/${auctionType.id}`);
};

const handleView = (auctionType: AuctionType) => {
  router.push(`/admin-spa/auction-types/view/${auctionType.id}`);
};

const handleDelete = async (auctionType: AuctionType) => {
  if (getAvailableItemsCount(auctionType) > 0) {
    showNotification('Cannot delete auction category with active items', 'error');
    return;
  }

  if (confirm('Are you sure you want to delete this auction category?')) {
    try {
      await api.delete(`/auction-types/${auctionType.id}`);
      showNotification('Auction category deleted successfully', 'success');
      await fetchAuctionTypes();
    } catch (err) {
      showNotification('Failed to delete auction category', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} auction categories?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} auction categories deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctionTypes();
    } catch (err) {
      showNotification('Failed to delete auction categories', 'error');
    }
  }
};

// Utility methods
const getAvailableItemsCount = (auctionType: AuctionType) => {
  return auctionType.items?.filter(item => !item.closed_by).length || 0;
};

const formatType = (type: string) => {
  return type.charAt(0).toUpperCase() + type.slice(1);
};

const getTypeVariant = (type: string) => {
  switch (type) {
    case 'online': return 'success';
    case 'cash': return 'warning';
    default: return 'default';
  }
};

// Lifecycle
onMounted(() => {
  fetchAuctionTypes();
});
</script>
