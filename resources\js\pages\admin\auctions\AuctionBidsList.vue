<template>
  <AdminListTemplate
    title="Bid List"
    subtitle="View and manage all auction bids"
    :loading="loading"
    :error="error"
    :items="auctions"
    :columns="columns"
    :selected-items="selectedAuctions"
    :show-bulk-actions="false"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Bid"
    empty-state-title="No bids found"
    empty-state-message="Bids will appear here when users place them."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
  >
    <template #filters>
      <Select
        v-model="filters.item_id"
        placeholder="All Items"
        :options="itemOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.status"
        placeholder="All Status"
        :options="statusOptions"
        @change="applyFilters"
      />
    </template>

    <template #rows="{ items }">
      <tr
        v-for="auction in items"
        :key="auction.id"
        :class="{ 'bg-yellow-50': auction.item?.closed_by }"
      >
        <!-- Item Column -->
        <td class="px-6 py-4">
          <div v-if="auction.item" class="flex items-center">
            <div class="flex-shrink-0 h-16 w-16">
              <img
                :src="auction.item.image || '/placeholder-image.jpg'"
                :alt="auction.item.name"
                class="h-16 w-16 rounded-lg object-cover"
              />
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">
                <a
                  :href="`/auctions/${auction.id}`"
                  class="hover:text-blue-600"
                >
                  {{ auction.item.name || '-' }}
                </a>
              </div>
            </div>
          </div>
        </td>

        <!-- Bidder Name -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ auction.user?.name || '-' }}
        </td>

        <!-- Auction Listing -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ auction.auctionType?.name || '-' }}
        </td>

        <!-- Bid Amount -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
          {{ formatCurrency(auction.bid_amount) }}
        </td>

        <!-- Description -->
        <td class="px-6 py-4 text-sm text-gray-900">
          {{ auction.description || '-' }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div v-if="auction.item?.closed_by" class="space-y-2">
            <div v-if="auction.closed_by && !auction.tagged_by" class="flex justify-center">
              <Button
                size="sm"
                variant="primary"
                @click="handleAddPayment(auction)"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                Add Payment
              </Button>
            </div>
            <Badge variant="success">Accepted</Badge>
          </div>
          <div v-else class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="success"
              @click="handleAcceptBid(auction)"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Accept
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleViewAuction(auction)"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleCancelBid(auction)"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              Cancel
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';
import type { Auction, Item } from '@/types';

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedAuctions = ref<number[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const auctions = ref<Auction[]>([]);
const items = ref<Item[]>([]);

// Filters
const filters = ref({
  item_id: '',
  status: '',
  search: ''
});

// Table columns
const columns = [
  { key: 'item', label: 'Item', sortable: false },
  { key: 'user', label: 'Name', sortable: true },
  { key: 'auction_type', label: 'Auction Listing', sortable: false },
  { key: 'bid_amount', label: 'Bid Amount', sortable: true },
  { key: 'description', label: 'Description', sortable: false }
];

// Computed
const itemOptions = computed(() => [
  { label: 'All Items', value: '' },
  ...items.value.map(item => ({
    label: item.name || '',
    value: item.id.toString()
  }))
]);

const statusOptions = computed(() => [
  { label: 'All Status', value: '' },
  { label: 'Open', value: 'open' },
  { label: 'Closed', value: 'closed' }
]);

// Methods
const fetchAuctions = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/auctions', params);
    auctions.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch auctions';
    console.error('Error fetching auctions:', err);
  } finally {
    loading.value = false;
  }
};

const fetchItems = async () => {
  try {
    const data = await api.get('/items');
    items.value = data.data || [];
  } catch (err) {
    console.error('Error fetching items:', err);
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchAuctions();
};

const handleCreate = () => {
  router.push('/auctions/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAuctions();
};

const handleSelectAll = (selected: boolean) => {
  selectedAuctions.value = selected ? auctions.value.map(a => a.id) : [];
};

const handleAcceptBid = async (auction: Auction) => {
  if (confirm('Are you sure you want to accept this bid as a winner?')) {
    try {
      await api.get(`/accept-bid/${auction.id}`);
      showNotification('Bid accepted successfully', 'success');
      await fetchAuctions();
    } catch (err) {
      showNotification('Failed to accept bid', 'error');
    }
  }
};

const handleViewAuction = (auction: Auction) => {
  router.push(`/auctions/${auction.id}`);
};

const handleCancelBid = async (auction: Auction) => {
  if (confirm('Are you sure you want to cancel this bid?')) {
    try {
      await api.delete(`/auctions/${auction.id}`);
      showNotification('Bid cancelled successfully', 'success');
      await fetchAuctions();
    } catch (err) {
      showNotification('Failed to cancel bid', 'error');
    }
  }
};

const handleAddPayment = (auction: Auction) => {
  // Implement payment modal logic
  console.log('Add payment for auction:', auction.id);
  showNotification('Payment functionality not yet implemented', 'info');
};

const formatCurrency = (amount: string | number | undefined) => {
  if (!amount) return '-';
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(num);
};

// Lifecycle
onMounted(() => {
  fetchAuctions();
  fetchItems();
});
</script>
