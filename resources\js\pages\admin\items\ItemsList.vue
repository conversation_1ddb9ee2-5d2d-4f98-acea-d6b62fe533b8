<template>
  <AdminListTemplate
    title="All Items"
    subtitle="View and manage all auction items"
    :loading="loading"
    :error="error"
    :items="items"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Item"
    empty-state-title="No items found"
    empty-state-message="Get started by adding your first auction item."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @view="handleView"
    @edit="handleEdit"
    @delete="handleDelete"
    @refresh="fetchItems"
  >
    <template #filters>
      <Select
        v-model="filters.status"
        placeholder="All Statuses"
        :options="statusOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.auction_type_id"
        placeholder="All Auction Types"
        :options="auctionTypeOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.branch_id"
        placeholder="All Branches"
        :options="branchOptions"
        @change="applyFilters"
      />
    </template>

    <!-- Table rows -->
    <template #rows="{ items }">
      <tr v-for="item in items" :key="item.id" class="hover:bg-gray-50">
        <!-- Bulk selection checkbox -->
        <td class="px-6 py-4">
          <input
            type="checkbox"
            :checked="selectedItems.includes(item.id.toString())"
            @change="toggleItemSelection(item.id)"
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
        </td>

        <!-- Image -->
        <td class="px-6 py-4">
          <div class="flex-shrink-0">
            <img
              :src="item.image || '/img/product.jpeg'"
              :alt="item.name"
              class="w-12 h-12 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity"
              @click="openImagePreview(item)"
            />
          </div>
        </td>

        <!-- Item Details -->
        <td class="px-6 py-4">
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ item.name }}
            </p>
            <p class="text-sm text-gray-500 truncate">
              Ref: {{ item.reference_number || 'N/A' }}
            </p>
            <p class="text-xs text-gray-400 truncate">
              Code: {{ item.code || 'N/A' }}
            </p>
          </div>
        </td>

        <!-- Owner -->
        <td class="px-6 py-4">
          <div v-if="item.user" class="text-sm">
            <p class="font-medium text-gray-900">{{ item.user.name }}</p>
            <p class="text-gray-500">{{ item.user.email }}</p>
          </div>
          <span v-else class="text-sm text-gray-400">No owner</span>
        </td>

        <!-- Auction Type -->
        <td class="px-6 py-4">
          <AdminBadge
            v-if="item.auction_type"
            :variant="getAuctionTypeBadgeVariant(item.auction_type.type)"
            size="sm"
          >
            {{ item.auction_type.name }}
          </AdminBadge>
          <span v-else class="text-sm text-gray-400">No type</span>
        </td>

        <!-- Amounts -->
        <td class="px-6 py-4">
          <div class="text-sm">
            <p class="font-medium text-gray-900">
              Target: {{ formatCurrency(item.target_amount) }}
            </p>
            <p class="text-gray-600">
              Current: {{ formatCurrency(item.bid_amount) }}
            </p>
          </div>
        </td>

        <!-- Status -->
        <td class="px-6 py-4">
          <AdminBadge
            :variant="item.closed_by ? 'success' : 'warning'"
            size="sm"
          >
            {{ item.closed_by ? 'Sold' : 'Available' }}
          </AdminBadge>
        </td>

        <!-- Dates -->
        <td class="px-6 py-4">
          <div class="text-xs text-gray-500">
            <p v-if="item.date_from">
              From: {{ formatDate(item.date_from) }}
            </p>
            <p v-if="item.date_to">
              To: {{ formatDate(item.date_to) }}
            </p>
            <p v-if="!item.date_from && !item.date_to">
              No dates set
            </p>
          </div>
        </td>

        <!-- Actions -->
        <td class="px-6 py-4">
          <div class="flex justify-end gap-2">
            <Button
              variant="ghost"
              size="sm"
              @click="handleView(item)"
              class="text-blue-600 hover:text-blue-700"
            >
              View
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="handleEdit(item)"
              class="text-green-600 hover:text-green-700"
            >
              Edit
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="handleDelete(item)"
              class="text-red-600 hover:text-red-700"
              :disabled="item?.closed_by"
            >
              Delete
            </Button>
          </div>
        </td>
      </tr>
    </template>

    <template #bulk-actions="{ selectedItems }">
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkStatusUpdate('close')"
        class="text-green-600 hover:text-green-700"
      >
        Mark as Sold
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkStatusUpdate('open')"
        class="text-blue-600 hover:text-blue-700"
      >
        Mark as Available
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkDelete"
        class="text-red-600 hover:text-red-700"
      >
        Delete Selected
      </Button>
    </template>
  </AdminListTemplate>

  <!-- Image Preview Modal -->
  <AdminModal v-if="showImagePreview" @close="closeImagePreview">
    <template #header>
      <h3 class="text-lg font-medium">{{ previewItem?.name }}</h3>
    </template>
    
    <div class="space-y-4">
      <img
        :src="previewItem?.image || '/img/product.jpeg'"
        :alt="previewItem?.name"
        class="w-full max-h-96 object-contain rounded-lg"
      />
      <div class="text-sm text-gray-600">
        <p><strong>Reference:</strong> {{ previewItem?.reference_number || 'N/A' }}</p>
        <p><strong>Code:</strong> {{ previewItem?.code || 'N/A' }}</p>
        <p v-if="previewItem?.description"><strong>Description:</strong> {{ previewItem.description }}</p>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end space-x-3">
        <Button variant="outline" @click="closeImagePreview">Close</Button>
        <Button variant="primary" @click="handleEdit(previewItem)">Edit Item</Button>
      </div>
    </template>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminBadge, AdminModal } from '@/components/admin/ui';
import { Button, Select } from '@/components/ui';
import { useAdminItems } from '@/stores/admin/items';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useAdminBranches } from '@/stores/admin/branches';
import { useNotifications } from '@/composables/useNotifications';

// Stores
const itemsStore = useAdminItems();
const auctionTypesStore = useAdminAuctionTypes();
const branchesStore = useAdminBranches();

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const selectedItems = ref<string[]>([]);
const showImagePreview = ref(false);
const previewItem = ref<any>(null);

// Filters
const filters = reactive({
  status: '',
  auction_type_id: '',
  branch_id: '',
  search: ''
});

// Computed
const items = computed(() => itemsStore.items?.data || []);
const loading = computed(() => itemsStore.loading);
const error = computed(() => itemsStore.error);
const currentPage = computed(() => itemsStore.items?.current_page || 1);
const totalPages = computed(() => itemsStore.items?.last_page || 1);
const totalItems = computed(() => itemsStore.items?.total || 0);
const perPage = computed(() => itemsStore.items?.per_page || 20);

// Table columns configuration
const columns = computed(() => [
  { key: 'image', label: 'Image', sortable: false },
  { key: 'name', label: 'Item Details', sortable: true },
  { key: 'owner', label: 'Owner', sortable: false },
  { key: 'auction_type', label: 'Auction Type', sortable: false },
  { key: 'amounts', label: 'Amounts', sortable: false },
  { key: 'status', label: 'Status', sortable: false },
  { key: 'dates', label: 'Dates', sortable: false }
]);

// Filter options
const statusOptions = computed(() => [
  { label: 'All Statuses', value: '' },
  { label: 'Available', value: 'available' },
  { label: 'Sold', value: 'sold' }
]);

const auctionTypeOptions = computed(() => [
  { label: 'All Types', value: '' },
  ...auctionTypesStore.auctionTypes.map(type => ({
    label: type.name,
    value: type.id.toString()
  }))
]);

const branchOptions = computed(() => [
  { label: 'All Branches', value: '' },
  ...branchesStore.branches.map(branch => ({
    label: branch.name,
    value: branch.id.toString()
  }))
]);

// Methods
const fetchItems = async () => {
  await itemsStore.fetchItems({
    ...filters,
    page: currentPage.value,
    per_page: perPage.value
  });
};

const applyFilters = () => {
  fetchItems();
};

const handleCreate = () => {
  router.push('/admin-spa/items/create');
};

const handleView = (item: any) => {
  router.push(`/admin-spa/items/view/${item.id}`);
};

const handleEdit = (item: any) => {
  router.push(`/admin-spa/items/edit/${item.id}`);
};

const handleDelete = async (item: any) => {
  if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
    try {
      await itemsStore.deleteItem(item.id);
      showNotification('Item deleted successfully', 'success');
      await fetchItems();
    } catch (error) {
      showNotification('Failed to delete item', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;

  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} items?`)) {
    try {
      await itemsStore.bulkDeleteItems(selectedItems.value);
      showNotification(`${selectedItems.value.length} items deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchItems();
    } catch (error) {
      showNotification('Failed to delete items', 'error');
    }
  }
};

const handleBulkStatusUpdate = async (status: 'open' | 'close') => {
  if (selectedItems.value.length === 0) return;

  try {
    await itemsStore.bulkUpdateItemStatus(selectedItems.value, status);
    const statusText = status === 'close' ? 'sold' : 'available';
    showNotification(`${selectedItems.value.length} items marked as ${statusText}`, 'success');
    selectedItems.value = [];
    await fetchItems();
  } catch (error) {
    showNotification('Failed to update item status', 'error');
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  fetchItems();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic
  fetchItems();
};

const handlePageChange = (page: number) => {
  fetchItems();
};

const handleSelectAll = (selected: boolean) => {
  if (selected) {
    selectedItems.value = items.value.map(item => item.id.toString());
  } else {
    selectedItems.value = [];
  }
};

const toggleItemSelection = (itemId: number) => {
  const itemIdStr = itemId.toString();
  const index = selectedItems.value.indexOf(itemIdStr);
  if (index > -1) {
    selectedItems.value.splice(index, 1);
  } else {
    selectedItems.value.push(itemIdStr);
  }
};

// Image preview methods
const openImagePreview = (item: any) => {
  previewItem.value = item;
  showImagePreview.value = true;
};

const closeImagePreview = () => {
  showImagePreview.value = false;
  previewItem.value = null;
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDate = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
};

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchItems(),
    auctionTypesStore.fetchAuctionTypes(),
    branchesStore.fetchBranches()
  ]);
});
</script>
