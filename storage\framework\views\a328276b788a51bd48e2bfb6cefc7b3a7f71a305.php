

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Accounts</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Account::class)): ?>
                <a href="<?php echo e(route('accounts.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Account
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Accounts List</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th>#</th>
                            <th class="text-left">
                                Customer Name
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.accounts.inputs.account_name'); ?>
                            </th>
                            <th class="text-right">
                                <?php echo app('translator')->get('crud.accounts.inputs.amount'); ?>
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.accounts.inputs.account_number'); ?>
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.accounts.inputs.account_type'); ?>
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($key + 1); ?></td>
                            <td>
                                <a href="/users/<?php echo e($account->user_id); ?>"> <?php echo e($account->user->name ?? '-'); ?> </a>
                            </td>
                            <td><?php echo e($account->account_name ?? '-'); ?></td>
                            <td class="text-right"><?php echo e($account->amount ?? '-'); ?></td>
                            <td><?php echo e($account->account_number ?? '-'); ?></td>
                            <td><?php echo e($account->account_type ?? '-'); ?></td>
                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $account)): ?>
                                    <a href="<?php echo e(route('accounts.edit', $account)); ?>">
                                        <button type="button" class="btn btn-primary btn-sm m-1">
                                            <i class="bi bi-pencil-square me-1"></i>
                                            Edit
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $account)): ?>
                                    <a href="<?php echo e(route('accounts.show', $account)); ?>">
                                        <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                            <i class="bi bi-eye me-1"></i>
                                            View
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $account)): ?>
                                    <form
                                        action="<?php echo e(route('accounts.destroy', $account)); ?>"
                                        method="POST"
                                        onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                    >
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button
                                            type="submit"
                                            class="btn btn-danger btn-sm m-1"
                                        >
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="text-center">No accounts found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn(".js-datatable", null, [0, 'asc'])
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/accounts/index.blade.php ENDPATH**/ ?>