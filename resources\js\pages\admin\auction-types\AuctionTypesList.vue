<template>
  <AdminListTemplate
    title="Auction Types"
    subtitle="Manage auction categories and types"
    :loading="loading"
    :error="error"
    :items="auctionTypes"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Auction Type"
    empty-state-title="No auction types found"
    empty-state-message="Get started by adding your first auction type."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @view="handleView"
    @edit="handleEdit"
    @delete="handleDelete"
    @refresh="fetchAuctionTypes"
  >
    <template #filters>
      <Select
        v-model="filters.type"
        placeholder="All Types"
        :options="typeOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.is_active"
        placeholder="All Status"
        :options="statusOptions"
        @change="applyFilters"
      />
    </template>

    <!-- Custom cell templates -->
    <template #cell-name="{ item }">
      <div class="min-w-0 flex-1">
        <p class="text-sm font-medium text-gray-900 truncate">
          {{ item.name }}
        </p>
        <p v-if="item.description" class="text-sm text-gray-500 truncate">
          {{ item.description }}
        </p>
      </div>
    </template>

    <template #cell-type="{ item }">
      <AdminBadge
        :variant="getTypeBadgeVariant(item.type)"
        size="sm"
      >
        {{ formatType(item.type) }}
      </AdminBadge>
    </template>

    <template #cell-status="{ item }">
      <AdminBadge
        :variant="item.is_active !== false ? 'success' : 'secondary'"
        size="sm"
      >
        {{ item.is_active !== false ? 'Active' : 'Inactive' }}
      </AdminBadge>
    </template>

    <template #cell-items_count="{ item }">
      <div class="text-sm text-gray-900">
        {{ item.items_count || 0 }} items
      </div>
    </template>

    <template #cell-auctions_count="{ item }">
      <div class="text-sm text-gray-900">
        {{ item.auctions_count || 0 }} auctions
      </div>
    </template>

    <template #cell-created_at="{ item }">
      <div class="text-xs text-gray-500">
        {{ formatDate(item.created_at) }}
      </div>
    </template>

    <template #actions="{ item }">
      <div class="flex justify-end gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="handleView(item)"
          class="text-blue-600 hover:text-blue-700"
        >
          View
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="handleEdit(item)"
          class="text-green-600 hover:text-green-700"
        >
          Edit
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="handleDelete(item)"
          class="text-red-600 hover:text-red-700"
          :disabled="(item.items_count || 0) > 0"
        >
          Delete
        </Button>
      </div>
    </template>

    <template #bulk-actions="{ selectedItems }">
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkStatusUpdate(true)"
        class="text-green-600 hover:text-green-700"
      >
        Activate Selected
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkStatusUpdate(false)"
        class="text-gray-600 hover:text-gray-700"
      >
        Deactivate Selected
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="handleBulkDelete"
        class="text-red-600 hover:text-red-700"
      >
        Delete Selected
      </Button>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminBadge } from '@/components/admin/ui';
import { Button, Select } from '@/components/ui';
import { useAdminAuctionTypes } from '@/stores/admin/auctionTypes';
import { useNotifications } from '@/composables/useNotifications';

// Store
const auctionTypesStore = useAdminAuctionTypes();

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const selectedItems = ref<string[]>([]);

// Filters
const filters = reactive({
  type: '',
  is_active: '',
  search: ''
});

// Computed
const auctionTypes = computed(() => auctionTypesStore.auctionTypesList || []);
const loading = computed(() => auctionTypesStore.loading);
const error = computed(() => auctionTypesStore.error);
const currentPage = computed(() => auctionTypesStore.currentPage);
const totalPages = computed(() => auctionTypesStore.lastPage);
const totalItems = computed(() => auctionTypesStore.totalAuctionTypes);
const perPage = computed(() => auctionTypesStore.paginatedAuctionTypes?.per_page || 20);

// Table columns configuration
const columns = computed(() => [
  { key: 'name', label: 'Name & Description', sortable: true },
  { key: 'type', label: 'Type', sortable: true },
  { key: 'status', label: 'Status', sortable: false },
  { key: 'items_count', label: 'Items', sortable: true },
  { key: 'auctions_count', label: 'Auctions', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true }
]);

// Filter options
const typeOptions = computed(() => [
  { label: 'All Types', value: '' },
  { label: 'Online Auction', value: 'online' },
  { label: 'Live Auction', value: 'live' },
  { label: 'Cash Sale', value: 'cash' }
]);

const statusOptions = computed(() => [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'true' },
  { label: 'Inactive', value: 'false' }
]);

// Methods
const fetchAuctionTypes = async () => {
  await auctionTypesStore.fetchAuctionTypes({
    ...filters,
    page: currentPage.value,
    per_page: perPage.value,
    is_active: filters.is_active ? filters.is_active === 'true' : undefined
  });
};

const applyFilters = () => {
  fetchAuctionTypes();
};

const handleCreate = () => {
  router.push('/admin-spa/auction-types/create');
};

const handleView = (auctionType: any) => {
  router.push(`/admin-spa/auction-types/view/${auctionType.id}`);
};

const handleEdit = (auctionType: any) => {
  router.push(`/admin-spa/auction-types/edit/${auctionType.id}`);
};

const handleDelete = async (auctionType: any) => {
  if ((auctionType.items_count || 0) > 0) {
    showNotification('Cannot delete auction type with associated items', 'error');
    return;
  }

  if (confirm(`Are you sure you want to delete "${auctionType.name}"?`)) {
    try {
      await auctionTypesStore.deleteAuctionType(auctionType.id);
      showNotification('Auction type deleted successfully', 'success');
      await fetchAuctionTypes();
    } catch (error) {
      showNotification('Failed to delete auction type', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} auction types?`)) {
    try {
      await auctionTypesStore.bulkDeleteAuctionTypes(selectedItems.value);
      showNotification(`${selectedItems.value.length} auction types deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchAuctionTypes();
    } catch (error) {
      showNotification('Failed to delete auction types', 'error');
    }
  }
};

const handleBulkStatusUpdate = async (isActive: boolean) => {
  if (selectedItems.value.length === 0) return;
  
  try {
    // This would need to be implemented in the store
    const statusText = isActive ? 'activated' : 'deactivated';
    showNotification(`${selectedItems.value.length} auction types ${statusText}`, 'success');
    selectedItems.value = [];
    await fetchAuctionTypes();
  } catch (error) {
    showNotification('Failed to update auction type status', 'error');
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  fetchAuctionTypes();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic
  fetchAuctionTypes();
};

const handlePageChange = (page: number) => {
  fetchAuctionTypes();
};

const handleSelectAll = (selected: boolean) => {
  if (selected) {
    selectedItems.value = auctionTypes.value.map(type => type.id.toString());
  } else {
    selectedItems.value = [];
  }
};

// Utility methods
const getTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatType = (type: string) => {
  switch (type) {
    case 'live': return 'Live Auction';
    case 'online': return 'Online Auction';
    case 'cash': return 'Cash Sale';
    default: return type;
  }
};

const formatDate = (date: string | null | undefined) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
};

// Lifecycle
onMounted(async () => {
  await fetchAuctionTypes();
});
</script>
