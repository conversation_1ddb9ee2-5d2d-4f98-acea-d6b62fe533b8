

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Roles</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Role::class)): ?>
                <a href="<?php echo e(route('roles.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Role
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">Roles List</h5>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left ps-3">
                                <?php echo app('translator')->get('crud.roles.inputs.name'); ?>
                            </th>
                            <th class="text-left ps-3">
                                Permissions
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="ps-3">
                                <a href="<?php echo e(route('roles.show', $role)); ?>" class="fw-bold">
                                    <?php echo e($role->name ?? '-'); ?>

                                </a>
                            </td>
                            <td class="ps-3">
                                <?php $__currentLoopData = $role->permissions->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="btn-soft-info btn btn-sm me-1 mb-1"><?php echo e($permission->name); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($role->permissions->count() > 3): ?>
                                    <span class="btn-soft-secondary btn btn-sm">+<?php echo e($role->permissions->count() - 3); ?> more</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $role)): ?>
                                    <a href="<?php echo e(route('roles.edit', $role)); ?>">
                                        <button type="button" class="btn btn-primary btn-sm m-1">
                                            <i class="bi bi-pencil-square me-1"></i>
                                            Edit
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $role)): ?>
                                    <a href="<?php echo e(route('roles.show', $role)); ?>">
                                        <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                            <i class="bi bi-eye me-1"></i>
                                            View
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $role)): ?>
                                    <form
                                        action="<?php echo e(route('roles.destroy', $role)); ?>"
                                        method="POST"
                                        onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                    >
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button
                                            type="submit"
                                            class="btn btn-danger btn-sm m-1"
                                        >
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="3" class="text-center">No roles found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn()
  });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/roles/index.blade.php ENDPATH**/ ?>