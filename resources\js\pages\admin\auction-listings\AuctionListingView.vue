<template>
  <AdminDetailTemplate
    :title="auction?.name || 'Auction Listing Details'"
    :subtitle="`${auction?.code || 'No code'} - ${auction?.closed_by ? 'Closed' : 'Active'}`"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button
          variant="outline"
          @click="handleEdit"
          :disabled="!auction"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit Auction
        </Button>
        <Button
          v-if="!auction?.closed_by"
          variant="outline"
          @click="handleClose"
          :disabled="!auction"
          class="text-orange-600 hover:text-orange-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Close Auction
        </Button>
        <Button
          v-else
          variant="outline"
          @click="handleReopen"
          :disabled="!auction"
          class="text-blue-600 hover:text-blue-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
          </svg>
          Reopen Auction
        </Button>
        <Button
          variant="outline"
          @click="handleDelete"
          :disabled="!auction"
          class="text-red-600 hover:text-red-700"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete Auction
        </Button>
      </div>
    </template>

    <div v-if="auction" class="space-y-6">
      <!-- Status and Type -->
      <Card class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Status & Type</h3>
            <p class="text-sm text-gray-500 mt-1">Current auction status and configuration</p>
          </div>
          <div class="flex space-x-3">
            <AdminBadge
              v-if="auction.auctionType"
              :variant="getAuctionTypeBadgeVariant(auction.auctionType.type)"
              size="lg"
            >
              {{ auction.auctionType.name }}
            </AdminBadge>
            <AdminBadge
              :variant="auction.closed_by ? 'success' : 'warning'"
              size="lg"
            >
              {{ auction.closed_by ? 'Closed' : 'Active' }}
            </AdminBadge>
          </div>
        </div>
      </Card>

      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Auction Name</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.name || 'Unnamed Auction' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Auction Code</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.code || 'No code assigned' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Auction Type</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.auctionType?.name || 'No type assigned' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Branch</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.branch?.name || 'No branch assigned' }}</p>
          </div>

          <div v-if="auction.description" class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Description</label>
            <p class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ auction.description }}</p>
          </div>
        </div>
      </Card>

      <!-- Associated Item -->
      <Card v-if="auction.item" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Associated Item</h3>
        
        <div class="flex items-start space-x-4">
          <img
            :src="auction.item.image || '/img/product.jpeg'"
            :alt="auction.item.name"
            class="w-32 h-32 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity"
            @click="viewItem"
          />
          <div class="flex-1">
            <h4 class="text-xl font-medium text-gray-900">{{ auction.item.name }}</h4>
            <p class="text-sm text-gray-600 mt-1">Ref: {{ auction.item.reference_number || 'N/A' }}</p>
            <p class="text-sm text-gray-600">Target Amount: {{ formatCurrency(auction.item.target_amount) }}</p>
            <p v-if="auction.item.description" class="text-sm text-gray-500 mt-2">
              {{ auction.item.description }}
            </p>
            <div class="flex items-center space-x-2 mt-3">
              <AdminBadge
                :variant="auction.item.closed_by ? 'success' : 'warning'"
                size="sm"
              >
                {{ auction.item.closed_by ? 'Sold' : 'Available' }}
              </AdminBadge>
              <Button
                variant="outline"
                size="sm"
                @click="viewItem"
                class="text-blue-600 hover:text-blue-700"
              >
                View Item Details
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- Bidding Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Bidding Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ formatCurrency(auction.bid_amount) }}</div>
            <div class="text-sm text-gray-500">Current Bid</div>
          </div>

          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ formatCurrency(auction.initial_payment) }}</div>
            <div class="text-sm text-gray-500">Initial Payment</div>
          </div>

          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ formatCurrency(auction.item?.target_amount) }}</div>
            <div class="text-sm text-gray-500">Target Amount</div>
          </div>
        </div>
      </Card>

      <!-- Schedule -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Start Date & Time</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auction.date_from) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">End Date & Time</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auction.date_to) }}</p>
          </div>
        </div>

        <!-- Duration indicator -->
        <div v-if="auction.date_from && auction.date_to" class="mt-4 p-3 bg-blue-50 rounded-lg">
          <p class="text-sm text-blue-800">
            <span class="font-medium">Duration:</span> {{ calculateDuration(auction.date_from, auction.date_to) }}
          </p>
        </div>
      </Card>

      <!-- Owner Information -->
      <Card v-if="auction.user" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Owner/Bidder Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Name</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.user.name }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Email</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.user.email }}</p>
          </div>
        </div>
      </Card>

      <!-- Transaction Information -->
      <Card v-if="auction.transaction" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Transaction Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Transaction ID</label>
            <p class="mt-1 text-sm text-gray-900">{{ auction.transaction_id || 'No transaction' }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Amount</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatCurrency(auction.transaction?.amount) }}</p>
          </div>
        </div>
      </Card>

      <!-- Timestamps -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Timestamps</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Created</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auction.created_at) }}</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Last Updated</label>
            <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(auction.updated_at) }}</p>
          </div>
        </div>
      </Card>
    </div>
  </AdminDetailTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminDetailTemplate, AdminBadge } from '@/components/admin/templates';
import { Card, Button } from '@/components/ui';
import { useAdminAuctions } from '@/stores/admin/auctions';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();
const route = useRoute();

// Stores
const auctionsStore = useAdminAuctions();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);

// Computed
const auctionId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);
const auction = computed(() => auctionsStore.currentAuction);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Auction Listings', href: '/admin-spa/auction-listings/list' },
  { label: auction.value?.name || 'Auction Details' }
]);

// Methods
const loadAuction = async () => {
  if (!auctionId.value) return;

  loading.value = true;
  error.value = null;

  try {
    await auctionsStore.fetchAuction(auctionId.value);
  } catch (err) {
    error.value = 'Failed to load auction';
    showNotification('Failed to load auction', 'error');
  } finally {
    loading.value = false;
  }
};

const handleEdit = () => {
  if (auctionId.value) {
    router.push(`/admin-spa/auction-listings/edit/${auctionId.value}`);
  }
};

const handleClose = async () => {
  if (!auction.value) return;

  if (confirm(`Are you sure you want to close this auction?`)) {
    try {
      await auctionsStore.closeAuction(auction.value.id);
      showNotification('Auction closed successfully', 'success');
      await loadAuction(); // Refresh data
    } catch (error) {
      showNotification('Failed to close auction', 'error');
    }
  }
};

const handleReopen = async () => {
  if (!auction.value) return;

  if (confirm(`Are you sure you want to reopen this auction?`)) {
    try {
      await auctionsStore.reopenAuction(auction.value.id);
      showNotification('Auction reopened successfully', 'success');
      await loadAuction(); // Refresh data
    } catch (error) {
      showNotification('Failed to reopen auction', 'error');
    }
  }
};

const handleDelete = async () => {
  if (!auction.value) return;

  if (confirm(`Are you sure you want to delete "${auction.value.name || 'this auction'}"?`)) {
    try {
      await auctionsStore.deleteAuction(auction.value.id);
      showNotification('Auction deleted successfully', 'success');
      router.push('/admin-spa/auction-listings/list');
    } catch (error) {
      showNotification('Failed to delete auction', 'error');
    }
  }
};

const viewItem = () => {
  if (auction.value?.item?.id) {
    router.push(`/admin-spa/items/view/${auction.value.item.id}`);
  }
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDateTime = (date: string | null | undefined) => {
  if (!date) return 'Not set';
  return new Date(date).toLocaleString();
};

const calculateDuration = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffMs = end.getTime() - start.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  } else {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  }
};

// Lifecycle
onMounted(() => {
  loadAuction();
});
</script>
