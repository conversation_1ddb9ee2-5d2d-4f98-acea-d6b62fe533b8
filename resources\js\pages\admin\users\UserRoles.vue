<template>
  <AdminListTemplate
    title="User Roles"
    subtitle="Manage user roles and permissions"
    :loading="loading"
    :error="error"
    :items="roles"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Role"
    empty-state-title="No roles found"
    empty-state-message="Start by creating a new user role."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @refresh="fetchRoles"
  >
    <template #rows="{ items }">
      <tr
        v-for="role in items"
        :key="role.id"
        class="hover:bg-gray-50"
      >
        <!-- Name Column -->
        <td class="px-6 py-4">
          <div class="text-sm font-medium text-gray-900">
            <a
              :href="`/admin-spa/roles/view/${role.id}`"
              class="hover:text-blue-600"
            >
              {{ role.name || '-' }}
            </a>
          </div>
        </td>

        <!-- Guard Name -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          <Badge variant="secondary">
            {{ role.guard_name || 'web' }}
          </Badge>
        </td>

        <!-- Permissions Count -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          <Badge variant="info">
            {{ role.permissions?.length || 0 }} permissions
          </Badge>
        </td>

        <!-- Users Count -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          <Badge variant="success">
            {{ role.users_count || 0 }} users
          </Badge>
        </td>

        <!-- Created At -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ formatDate(role.created_at) }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
          <div class="flex justify-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              @click="handleEdit(role)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="info"
              @click="handleView(role)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </Button>
            <Button
              size="sm"
              variant="danger"
              @click="handleDelete(role)"
              :disabled="role.name === 'Super Admin'"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Badge } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';
import { api } from '@/utils/api';

interface Role {
  id: number;
  name: string;
  guard_name: string;
  users_count?: number;
  permissions?: Array<{
    id: number;
    name: string;
  }>;
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// Composables
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const selectedItems = ref<string[]>([]);
const currentPage = ref(1);
const perPage = ref(20);
const totalItems = ref(0);
const totalPages = ref(1);
const roles = ref<Role[]>([]);

// Filters
const filters = ref({
  search: ''
});

// Table columns
const columns = [
  { key: 'name', label: 'Role Name', sortable: true },
  { key: 'guard_name', label: 'Guard', sortable: true },
  { key: 'permissions', label: 'Permissions', sortable: false },
  { key: 'users_count', label: 'Users', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true }
];

// Methods
const fetchRoles = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value.toString(),
      per_page: perPage.value.toString(),
      ...filters.value
    };

    const data = await api.get('/admin/roles', params);
    roles.value = data.data;
    totalItems.value = data.meta?.total || 0;
    totalPages.value = data.meta?.last_page || 1;
    currentPage.value = data.meta?.current_page || 1;
  } catch (err) {
    error.value = 'Failed to fetch roles';
    console.error('Error fetching roles:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  currentPage.value = 1;
  fetchRoles();
};

const handleCreate = () => {
  router.push('/admin-spa/roles/create');
};

const handleSearch = (query: string) => {
  filters.value.search = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic if needed
  console.log('Sort:', column, order);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchRoles();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? roles.value.map(r => r.id.toString()) : [];
};

const handleEdit = (role: Role) => {
  router.push(`/admin-spa/roles/edit/${role.id}`);
};

const handleView = (role: Role) => {
  router.push(`/admin-spa/roles/view/${role.id}`);
};

const handleDelete = async (role: Role) => {
  if (role.name === 'Super Admin') {
    showNotification('Cannot delete Super Admin role', 'error');
    return;
  }

  if (confirm('Are you sure you want to delete this role?')) {
    try {
      await api.delete(`/admin/roles/${role.id}`);
      showNotification('Role deleted successfully', 'success');
      await fetchRoles();
    } catch (err) {
      showNotification('Failed to delete role', 'error');
    }
  }
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;

  if (confirm(`Are you sure you want to delete ${selectedItems.value.length} roles?`)) {
    try {
      // Implement bulk delete logic
      showNotification(`${selectedItems.value.length} roles deleted successfully`, 'success');
      selectedItems.value = [];
      await fetchRoles();
    } catch (err) {
      showNotification('Failed to delete roles', 'error');
    }
  }
};

// Utility methods
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(() => {
  fetchRoles();
});
</script>