<template>
  <AdminDetailTemplate
    title="Bulk Import Items"
    subtitle="Import multiple items from CSV/Excel files"
    :loading="loading"
    :error="error"
    @back="handleBack"
  >
    <div class="space-y-6">
      <!-- Upload Section -->
      <Card>
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Upload File</h3>
          
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div class="space-y-4">
              <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
              </div>
              
              <div>
                <label for="file-upload" class="cursor-pointer">
                  <span class="mt-2 block text-sm font-medium text-gray-900">
                    Drop files here or click to upload
                  </span>
                  <span class="mt-1 block text-xs text-gray-500">
                    CSV, Excel files up to 10MB
                  </span>
                </label>
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  class="sr-only"
                  accept=".csv,.xlsx,.xls"
                  @change="handleFileSelect"
                />
              </div>
            </div>
          </div>
          
          <div v-if="selectedFile" class="mt-4 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg class="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-blue-900">{{ selectedFile.name }}</p>
                  <p class="text-xs text-blue-700">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                @click="removeFile"
              >
                Remove
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- Import Options -->
      <Card v-if="selectedFile">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Import Options</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Skip Header Row
              </label>
              <div class="flex items-center">
                <input
                  id="skip-header"
                  v-model="importOptions.skipHeader"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="skip-header" class="ml-2 text-sm text-gray-600">
                  First row contains column headers
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Update Existing Items
              </label>
              <div class="flex items-center">
                <input
                  id="update-existing"
                  v-model="importOptions.updateExisting"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label for="update-existing" class="ml-2 text-sm text-gray-600">
                  Update items if they already exist
                </label>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- Preview Section -->
      <Card v-if="previewData.length > 0">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th v-for="header in previewHeaders" :key="header" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ header }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(row, index) in previewData.slice(0, 5)" :key="index">
                  <td v-for="(cell, cellIndex) in row" :key="cellIndex" class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ cell }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <p class="mt-4 text-sm text-gray-500">
            Showing first 5 rows of {{ previewData.length }} total rows
          </p>
        </div>
      </Card>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-4">
        <Button
          variant="outline"
          @click="handleBack"
        >
          Cancel
        </Button>
        
        <Button
          v-if="selectedFile"
          :loading="processing"
          @click="processImport"
        >
          Import Items
        </Button>
      </div>
    </div>
  </AdminDetailTemplate>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminDetailTemplate } from '@/components/admin/templates';
import { Card, Button } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const processing = ref(false);
const selectedFile = ref<File | null>(null);
const previewData = ref<any[]>([]);
const previewHeaders = ref<string[]>([]);

// Import options
const importOptions = reactive({
  skipHeader: true,
  updateExisting: false
});

// Methods
const handleBack = () => {
  router.push('/admin-spa/items/list');
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (file) {
    selectedFile.value = file;
    parseFile(file);
  }
};

const removeFile = () => {
  selectedFile.value = null;
  previewData.value = [];
  previewHeaders.value = [];
};

const parseFile = async (file: File) => {
  try {
    // TODO: Implement actual file parsing
    // For now, show mock preview data
    previewHeaders.value = ['Name', 'Description', 'Category', 'Starting Price', 'Status'];
    previewData.value = [
      ['Vintage Watch', 'Antique pocket watch from 1920s', 'Jewelry', '$150.00', 'Active'],
      ['Modern Laptop', 'High-performance gaming laptop', 'Electronics', '$800.00', 'Active'],
      ['Wooden Chair', 'Handcrafted oak dining chair', 'Furniture', '$75.00', 'Active']
    ];
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'Parse Error',
      message: 'Failed to parse the selected file.'
    });
  }
};

const processImport = async () => {
  if (!selectedFile.value) return;
  
  processing.value = true;
  
  try {
    // TODO: Implement actual import logic
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    showNotification({
      type: 'success',
      title: 'Import Successful',
      message: `Successfully imported ${previewData.value.length} items.`
    });
    
    router.push('/admin-spa/items/list');
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'Import Failed',
      message: 'Failed to import items. Please try again.'
    });
  } finally {
    processing.value = false;
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
