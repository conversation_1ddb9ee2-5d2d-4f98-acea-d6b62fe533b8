

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Users</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end align-items-center">
                <form method="GET" id="filter" class="d-flex align-items-center me-3 mb-0">
                    <div class="me-2" style="min-width: 200px;">
                        <select class="form-select form-select-sm" name="role_id" onchange="this.form.submit()" autocomplete="off">
                            <option value="0">All Roles</option>
                            <?php $__currentLoopData = App\Models\Role::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option <?php if(request()->role_id == $role->id): ?> selected <?php endif; ?> value="<?php echo e($role->id); ?>">
                                <?php echo e($role->name ?? ''); ?>

                            </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </form>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\User::class)): ?>
                <a href="<?php echo e(route('users.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add User
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>


    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">Users List</h5>
                </div>
            </div>
        </div>


        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left ps-3">
                                <?php echo app('translator')->get('crud.users.inputs.name'); ?>
                            </th>
                            <th class="text-left ps-3">
                                <?php echo app('translator')->get('crud.users.inputs.email'); ?>
                            </th>
                            <th class="text-left ps-3">
                                Roles
                            </th>
                            <th class="text-left ps-3">
                                Branch
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="ps-3">
                              <a class="d-flex align-items-center" href="/users/<?php echo e($user->id ?? '-'); ?>">
                                <div class="flex-shrink-0">
                                  <img class="avatar avatar-lg" src="<?php echo e($user->image ?? '-'); ?>" alt="Image Description">
                                </div>
                                <div class="flex-grow-1 ms-3">
                                  <h5 class="text-inherit mb-0">
                                      <?php echo e($user->name ?? '-'); ?>

                                  </h5>
                                  <small><?php echo e($user->phone ?? '-'); ?></small>
                                </div>
                              </a>
                            </td>
                            <td class="ps-3"><?php echo e($user->email ?? '-'); ?></td>
                            <td class="ps-3">
                                <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="btn-soft-info btn btn-sm"> <?php echo e($role->name ?? '-'); ?> </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </td>
                            <td class="ps-3"><?php echo e($user->branch->name ?? '-'); ?></td>

                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $user)): ?>
                                    <a href="<?php echo e(route('users.edit', $user)); ?>">
                                        <button type="button" class="btn btn-primary btn-sm m-1">
                                            <i class="bi bi-pencil-square me-1"></i>
                                            Edit
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $user)): ?>
                                    <a href="<?php echo e(route('users.show', $user)); ?>">
                                        <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                            <i class="bi bi-eye me-1"></i>
                                            View
                                        </button>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $user)): ?>
                                    <form
                                        action="<?php echo e(route('users.destroy', $user)); ?>"
                                        method="POST"
                                        onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                    >
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button
                                            type="submit"
                                            class="btn btn-danger btn-sm m-1"
                                        >
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">No users found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
   dataTableBtn()
  });
</script>


<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/users/index.blade.php ENDPATH**/ ?>