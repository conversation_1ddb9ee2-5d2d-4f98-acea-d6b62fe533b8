// Global type definitions for Vertigo AMS

export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
  phone?: string;
  avatar?: string;
  branch?: Branch;
  items?: Item[];
}

export interface Role {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

export interface Auction {
  id: number;
  name?: string;
  code?: string;
  bid_amount?: string;
  item_id?: number;
  tagged_by?: number;
  transaction_id?: number;
  auction_type_id?: number;
  closed_by?: number;
  date_from?: string;
  date_to?: string;
  description?: string;
  branch_id?: number;
  user_id?: number;
  created_by?: number;
  updated_by?: number;
  appoved_by?: number;
  status_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  initial_payment?: number;
  item?: Item;
  auctionType?: AuctionType;
  transaction?: any;

  // Legacy fields for backward compatibility
  title?: string;
  start_date?: string;
  end_date?: string;
  status?: 'draft' | 'active' | 'completed' | 'cancelled';
  items?: Item[];
}

export interface Item {
  id: number;
  name: string;
  title?: string;
  description?: string;
  target_amount?: number;
  bid_amount?: number;
  auction_id?: number;
  auction_type_id?: number;
  auction_type?: AuctionType;
  status?: 'active' | 'sold' | 'unsold';
  type?: string;
  cropped?: string;
  image?: string;
  code?: string;
  reference_number?: string;
  date_from?: string;
  date_to?: string;
  closed_by?: number;
  branch_id?: number;
  created_at: string;
  updated_at: string;
  images?: ItemImage[];
  media?: MediaItem[];
}

export interface MediaItem {
  id?: number;
  url?: string;
  original_url?: string;
  alt_text?: string;
  is_primary?: boolean;
}

export interface Branch {
  id: number;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

export interface AuctionType {
  id: number;
  name: string;
  type: 'cash' | 'online' | 'live';
  bid_amount?: string;
  description?: string;
  status_id?: number;
  branch_id?: number;
  created_by?: number;
  updated_by?: number;
  date_from?: string;
  date_to?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  image?: string;
  auction?: any;
  joined?: boolean;
  cropped?: string;
  media?: MediaItem[];
  items?: Item[];
}

export interface ItemImage {
  id: number;
  item_id: number;
  url: string;
  alt_text?: string;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
}

export interface Bid {
  id: number;
  amount: number;
  item_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface Account {
  id: number;
  account_name?: string;
  account_number?: string;
  account_type?: string;
  amount?: number;
  bank_name?: string;
  branch_id?: number;
  user_id?: number;
  created_by?: number;
  updated_by?: number;
  status_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  user?: User;
  createdBy?: User;
  branch?: Branch;
}

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T = any> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

// Component prop types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps {
  modelValue?: string | number;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
}

// Table component types
export interface TableColumn<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: T, index: number) => string | any;
  headerClass?: string;
  cellClass?: string;
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  size?: 'sm' | 'md' | 'lg';
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  selectable?: boolean;
  selectedRows?: T[];
  emptyMessage?: string;
  stickyHeader?: boolean;
  maxHeight?: string;
}

export interface TableSortEvent {
  column: string;
  direction: 'asc' | 'desc';
}

export interface TableSelectEvent<T = any> {
  selectedRows: T[];
  row?: T;
  isSelected?: boolean;
}

// Navigation component types
export interface NavItem {
  key: string;
  label: string;
  href?: string;
  icon?: any;
  active?: boolean;
  children?: NavItem[];
  danger?: boolean;
}

export interface BreadcrumbItem {
  key?: string;
  label: string;
  href?: string;
  icon?: any;
}

export interface DropdownItem {
  key: string;
  label?: string;
  href?: string;
  icon?: any;
  rightIcon?: any;
  description?: string;
  disabled?: boolean;
  danger?: boolean;
  type?: 'item' | 'divider' | 'header';
}

export interface SelectItem {
  key: string;
  label?: string;
  value?: any;
  icon?: any;
  description?: string;
  disabled?: boolean;
  danger?: boolean;
  type?: 'item' | 'divider' | 'header';
}

export interface BadgeProps {
  text?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  rounded?: boolean;
  outlined?: boolean;
  closable?: boolean;
  icon?: any;
  iconPosition?: 'left' | 'right';
  dot?: boolean;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  total?: number;
  perPage?: number;
  maxVisiblePages?: number;
  showInfo?: boolean;
  showFirstLast?: boolean;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
}

// Form component types
export interface FormInputProps {
  modelValue?: string | number;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date' | 'time' | 'datetime-local';
  label?: string;
  placeholder?: string;
  helpText?: string;
  error?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  clearable?: boolean;
  loading?: boolean;
  prefixIcon?: any;
  suffixIcon?: any;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
}

export interface FormTextareaProps {
  modelValue?: string;
  label?: string;
  placeholder?: string;
  helpText?: string;
  error?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  rows?: number;
  maxLength?: number;
  showCharCount?: boolean;
  showWordCount?: boolean;
  autoResize?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
}

export interface FileItem {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress?: number;
  error?: string;
  url?: string;
}

export interface FormFileUploadProps {
  modelValue?: FileItem[];
  label?: string;
  helpText?: string;
  error?: string;
  accept?: string;
  acceptText?: string;
  multiple?: boolean;
  disabled?: boolean;
  required?: boolean;
  maxSize?: number;
  maxFiles?: number;
  uploadUrl?: string;
  autoUpload?: boolean;
}

// Store types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface AuctionState {
  auctions: Auction[];
  currentAuction: Auction | null;
  isLoading: boolean;
  error: string | null;
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
