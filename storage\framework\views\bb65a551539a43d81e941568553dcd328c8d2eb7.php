<div class="container mb-5">
    <div class="d-flex align-items-end pt-4">
        <h5 class="mb-0">
            <?php echo e(config('app.name') ?? ''); ?>

        </h5>

        <button id="create-backup" class="btn btn-primary btn-sm ml-auto px-3">
            Create Full Backup
        </button>
        <div class="dropdown ml-3">
            <a href="/" class="btn btn-primary btn-sm  px-3">
                Home
            </a>
            <button class="btn btn-primary btn-sm dropdown-toggle px-3" id="dropdownMenuButton"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <svg xmlns="http://www.w3.org/2000/svg" width="0.7875rem" height="0.7875rem" viewBox="0 0 24 24"
                     fill="currentColor">
                    <path class="heroicon-ui" d="M4 5h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2z"/>
                </svg>
            </button>
            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item" href="#" id="create-backup-only-db" wire:click.prevent="">
                    Create database backup
                </a>
                <a class="dropdown-item" href="#" id="create-backup-only-files" wire:click.prevent="">
                    Create files backup
                </a>
                <hr>
                <a class="dropdown-item" href="#" onclick="return confirm('Make sure you have downloaded backup')" id="reset-dababase" wire:click.prevent="">
                    Reset Database
                </a>                

                <a class="dropdown-item" href="#" onclick="return confirm('Make sure you have downloaded backup')" id="restore-dababase" wire:click.prevent="">
                    Restore Database
                </a>

            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex align-items-end">
                    <button class="btn btn-primary btn-sm btn-refresh ml-auto"
                            wire:loading.class="loading"
                            wire:loading.attr="disabled"
                            wire:click="updateBackupStatuses"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="0.7875rem" height="0.7875rem" viewBox="0 0 24 24"
                             fill="currentColor">
                            <path class="heroicon-ui" d="M6 18.7V21a1 1 0 0 1-2 0v-5a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2H7.1A7 7 0 0 0 19 12a1 1 0 1 1 2 0 9 9 0 0 1-15 6.7zM18 5.3V3a1 1 0 0 1 2 0v5a1 1 0 0 1-1 1h-5a1 1 0 0 1 0-2h2.9A7 7 0 0 0 5 12a1 1 0 1 1-2 0 9 9 0 0 1 15-6.7z"/>
                        </svg>
                    </button>
                </div>
                <table class="table table-hover mb-0">
                    <thead>
                    <tr>
                        <th scope="col">Disk</th>
                        <th scope="col">Healthy</th>
                        <th scope="col">Amount of backups</th>
                        <th scope="col">Newest backup</th>
                        <th scope="col">Used storage</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $backupStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $backupStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($backupStatus['disk']); ?></td>
                            <td>
                                <?php if($backupStatus['healthy']): ?>
                                    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" height="24px">
                                        <path d="M2.93 17.07A10 10 0 1 0 17.07 2.93 10 10 0 0 0 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM4 10l2-2 3 3 5-5 2 2-7 7-5-5z" fill="var(--success)" fill-rule="evenodd"/>
                                    </svg>
                                <?php else: ?>
                                    <svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" height="24px">
                                        <path d="M11.41 10l2.83-2.83-1.41-1.41L10 8.59 7.17 5.76 5.76 7.17 8.59 10l-2.83 2.83 1.41 1.41L10 11.41l2.83 2.83 1.41-1.41L11.41 10zm-8.48 7.07A10 10 0 1 0 17.07 2.93 10 10 0 0 0 2.93 17.07zm1.41-1.41A8 8 0 1 0 15.66 4.34 8 8 0 0 0 4.34 15.66z" fill="var(--danger)" fill-rule="evenodd"/>
                                    </svg>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($backupStatus['amount']); ?></td>
                            <td><?php echo e($backupStatus['newest']); ?></td>
                            <td><?php echo e($backupStatus['usedStorage']); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <div class="card shadow-sm">
                <div class="card-header d-flex align-items-end">
                    <?php if(count($disks)): ?>
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                            <?php $__currentLoopData = $disks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $disk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="btn btn-outline-secondary <?php echo e($activeDisk === $disk ? 'active' : ''); ?>"
                                       wire:click="getFiles('<?php echo e($disk); ?>')"
                                >
                                    <input type="radio" name="options" <?php echo e($activeDisk === $disk ? 'checked' : ''); ?>>
                                    <?php echo e($disk); ?>

                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                    <button class="btn btn-primary btn-sm btn-refresh ml-auto"
                            wire:loading.class="loading"
                            wire:loading.attr="disabled"
                            wire:click="getFiles"
                            <?php echo e($activeDisk ? '' : 'disabled'); ?>

                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="0.7875rem" height="0.7875rem" viewBox="0 0 24 24" fill="currentColor">
                            <path class="heroicon-ui" d="M6 18.7V21a1 1 0 0 1-2 0v-5a1 1 0 0 1 1-1h5a1 1 0 1 1 0 2H7.1A7 7 0 0 0 19 12a1 1 0 1 1 2 0 9 9 0 0 1-15 6.7zM18 5.3V3a1 1 0 0 1 2 0v5a1 1 0 0 1-1 1h-5a1 1 0 0 1 0-2h2.9A7 7 0 0 0 5 12a1 1 0 1 1-2 0 9 9 0 0 1 15-6.7z"/>
                        </svg>
                    </button>
                </div>

                <table class="table table-hover mb-0">
                    <thead>
                    <tr>
                        <th scope="col">Path</th>
                        <th scope="col">Created at</th>
                        <th scope="col">Size</th>
                        <th scope="col"></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($file['path']); ?></td>
                            <td><?php echo e($file['date']); ?></td>
                            <td><?php echo e($file['size']); ?></td>
                            <td class="text-right pr-3">
                                <a class="action-button mr-2" href="#" target="_blank" wire:click.prevent="downloadFile('<?php echo e($file['path']); ?>')">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                        <path class="heroicon-ui" d="M11 14.59V3a1 1 0 0 1 2 0v11.59l3.3-3.3a1 1 0 0 1 1.4 1.42l-5 5a1 1 0 0 1-1.4 0l-5-5a1 1 0 0 1 1.4-1.42l3.3 3.3zM3 17a1 1 0 0 1 2 0v3h14v-3a1 1 0 0 1 2 0v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3z"/>
                                    </svg>
                                </a>
                                <a class="action-button" href="#" target="_blank" wire:click.prevent="showDeleteModal(<?php echo e($loop->index); ?>)">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                        <path class="heroicon-ui" d="M8 6V4c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v2h5a1 1 0 0 1 0 2h-1v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V8H3a1 1 0 1 1 0-2h5zM6 8v12h12V8H6zm8-2V4h-4v2h4zm-4 4a1 1 0 0 1 1 1v6a1 1 0 0 1-2 0v-6a1 1 0 0 1 1-1zm4 0a1 1 0 0 1 1 1v6a1 1 0 0 1-2 0v-6a1 1 0 0 1 1-1z"/>
                                    </svg>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php if(!count($files)): ?>
                        <tr>
                            <td class="text-center" colspan="4">
                                <?php echo e('No backups present'); ?>

                            </td>
                        </tr>
                    <?php endif; ?>
                    </tbody>
                </table>

                <input type="file" style="opacity: 0;" id="inputFile" name="database">

                <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog"
                     aria-labelledby="exampleModalCenterTitle"
                     aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-body">
                                <h5 class="modal-title mb-3">Delete backup</h5>
                                <?php if($deletingFile): ?>
                                <span class="text-muted">
                                    Are you sure you want to delete the backup created at <?php echo e($deletingFile['date']); ?> ?
                                </span>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary cancel-button" data-dismiss="modal">
                                    Cancel
                                </button>
                                <button type="button" class="btn btn-danger delete-button" wire:click="deleteFile">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:load', function () {
            window.livewire.find('<?php echo e($_instance->id); ?>').updateBackupStatuses()

            window.livewire.find('<?php echo e($_instance->id); ?>').on('backupStatusesUpdated', function () {
                window.livewire.find('<?php echo e($_instance->id); ?>').getFiles()
            })

            window.livewire.find('<?php echo e($_instance->id); ?>').on('showErrorToast', function (message) {
                Toastify({
                    text: message,
                    duration: 10000,
                    gravity: 'bottom',
                    position: 'right',
                    backgroundColor: 'red',
                    className: 'toastify-custom',
                }).showToast()
            })

            const backupFun = function (option = '') {
                Toastify({
                    text: 'Creating a new backup in the background...' + (option ? ' (' + option + ')' : ''),
                    duration: 5000,
                    gravity: 'bottom',
                    position: 'right',
                    backgroundColor: '#1fb16e',
                    className: 'toastify-custom',
                }).showToast()

                window.livewire.find('<?php echo e($_instance->id); ?>').createBackup(option)
            }

            $('#create-backup').on('click', function () {
                backupFun()
            })
            $('#create-backup-only-db').on('click', function () {
                backupFun('only-db')
            })
            $('#create-backup-only-files').on('click', function () {
                backupFun('only-files')
            })            

            $('#restore-dababase').on('click', function () {
                inputFile.click();
                inputFile.onchange = function(e) {
                    var file = e.target.files[0];
                    var formData = new FormData();
                    formData.append("database", file);
                    Toastify({
                        text: 'Restoring  Database Please wait...',
                        duration: 1000000,
                        gravity: 'bottom',
                        position: 'right',
                        backgroundColor: 'red',
                        className: 'toastify-custom',
                    }).showToast()
                    // backupFun('only-db');
                    axios.post('/restore-database',formData).then( res => {
                        $('.toastify').remove();
                        Toastify({
                            text: res.data.message,
                            duration: 5000,
                            gravity: 'bottom',
                            position: 'right',
                            backgroundColor: '#1fb16e',
                            className: 'toastify-custom',
                        }).showToast();                    
                    })

                }
            });

            $('#reset-dababase').on('click', function () {
                Toastify({
                    text: 'Resetting Database Please wait...',
                    duration: 1000000,
                    gravity: 'bottom',
                    position: 'right',
                    backgroundColor: 'red',
                    className: 'toastify-custom',
                }).showToast()
                // backupFun('only-db');
                axios.post('/reset-database').then( res => {
                    $('.toastify').remove();
                    Toastify({
                        text: res.data.message,
                        duration: 3000,
                        gravity: 'bottom',
                        position: 'right',
                        backgroundColor: '#1fb16e',
                        className: 'toastify-custom',
                    }).showToast();                    
                })


            });

            const deleteModal = $('#deleteModal')
            window.livewire.find('<?php echo e($_instance->id); ?>').on('showDeleteModal', function () {
                deleteModal.modal('show')
            })
            window.livewire.find('<?php echo e($_instance->id); ?>').on('hideDeleteModal', function () {
                deleteModal.modal('hide')
            })

            deleteModal.on('hidden.bs.modal', function () {
                window.livewire.find('<?php echo e($_instance->id); ?>').deletingFile = null
            })
        })
    </script>
</div>


<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/vendor/laravel_backup_panel/livewire/app.blade.php ENDPATH**/ ?>