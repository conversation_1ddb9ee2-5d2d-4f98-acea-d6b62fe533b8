

<?php $__env->startSection('content'); ?>

<?php if (isset($component)) { $__componentOriginal80611e414e0bfe69fd8a18b3920f7945c9dc70f1 = $component; } ?>
<?php $component = App\View\Components\SmModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('sm-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\SmModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('class', null, []); ?> add-setting <?php $__env->endSlot(); ?>
     <?php $__env->slot('title', null, []); ?> Add Setting <?php $__env->endSlot(); ?>

    <form action="/settings" enctype="multipart/form-data" method="POST" id="addSetting">
        <?php echo csrf_field(); ?>
        <div class="mb-3">
            <label class="form-label">Name</label>
            <input type="text" class="form-control" name="key" required placeholder="Enter setting name">
        </div>

        <div class="mb-3">
            <label class="form-label">Value</label>
            <textarea class="form-control" name="value" placeholder="Enter text value" required rows="3"></textarea>
        </div>

        <div class="mb-3">
            <label class="form-label">File (Optional)</label>
            <input type="file" class="form-control" name="media">
            <small class="form-text text-muted">You have an option to attach a file.</small>
        </div>
    </form>
     <?php $__env->slot('footer', null, []); ?> 
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" onclick="addSetting.submit()">
            <i class="bi bi-save me-1"></i> Save
        </button>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal80611e414e0bfe69fd8a18b3920f7945c9dc70f1)): ?>
<?php $component = $__componentOriginal80611e414e0bfe69fd8a18b3920f7945c9dc70f1; ?>
<?php unset($__componentOriginal80611e414e0bfe69fd8a18b3920f7945c9dc70f1); ?>
<?php endif; ?>


<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Settings</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if(auth()->user()->isSuperAdmin()): ?>
                <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target=".add-setting">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Option
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">System Settings</h5>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left ps-3">
                                Key
                            </th>
                            <th class="text-left ps-3">
                                Value
                            </th>
                            <th class="text-left ps-3">
                                Created By
                            </th>
                            <th class="text-center">
                                <?php echo app('translator')->get('crud.common.actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="ps-3 fw-bold"><?php echo e($setting->key ?? '-'); ?></td>
                            <td class="ps-3">
                                <?php if($setting->path): ?>
                                <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.partials.thumbnail','data' => ['src' => ''.e($setting->path ?? '').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('partials.thumbnail'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => ''.e($setting->path ?? '').'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                                <?php else: ?>
                                <?php echo $setting->value ?? '-'; ?>

                                <?php endif; ?>
                            </td>
                            <td class="ps-3"><?php echo e($setting->createdBy->name ?? '-'); ?></td>
                            <td class="text-center" style="width: 134px;">
                                <div role="group" aria-label="Row Actions" class="btn-group">
                                    <form
                                        action="<?php echo e(route('settings.destroy', $setting)); ?>"
                                        method="POST"
                                        onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                    >
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button
                                            type="submit"
                                            class="btn btn-danger btn-sm m-1"
                                        >
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="4" class="text-center">No settings found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn()
  });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/settings/index.blade.php ENDPATH**/ ?>