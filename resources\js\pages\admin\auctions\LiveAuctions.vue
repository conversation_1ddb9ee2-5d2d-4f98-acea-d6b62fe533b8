<template>
  <AdminListTemplate
    title="Live Auctions"
    subtitle="Monitor currently active auctions"
    :loading="loading"
    :error="error"
    :items="auctions"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="false"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Create Auction"
    empty-state-title="No live auctions"
    empty-state-message="There are currently no active auctions."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @view="handleView"
    @edit="handleEdit"
    @refresh="fetchAuctions"
  >
    <template #filters>
      <Select
        v-model="filters.category"
        :options="categoryOptions"
        placeholder="Filter by category"
        class="w-48"
        @change="applyFilters"
      />
    </template>

    <template #item-actions="{ item }">
      <div class="flex items-center space-x-2">
        <Button
          size="sm"
          variant="outline"
          @click="handleMonitor(item)"
        >
          Monitor
        </Button>
        <Button
          size="sm"
          variant="danger"
          @click="handleEnd(item)"
        >
          End Now
        </Button>
      </div>
    </template>
  </AdminListTemplate>

  <!-- End Auction Modal -->
  <AdminModal
    v-model="showEndModal"
    title="End Auction"
    :loading="ending"
    @confirm="confirmEnd"
    @cancel="showEndModal = false"
  >
    <p class="text-gray-600">
      Are you sure you want to end this auction? This action cannot be undone.
    </p>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminModal } from '@/components/admin/ui';
import { Button, Select } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const auctions = ref([]);
const selectedItems = ref([]);
const showEndModal = ref(false);
const ending = ref(false);
const auctionToEnd = ref<any>(null);

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(25);

// Filters
const filters = reactive({
  search: '',
  category: '',
  sort_by: 'end_date',
  sort_direction: 'asc'
});

// Table columns
const columns = [
  { key: 'title', label: 'Auction Title', sortable: true },
  { key: 'current_bid', label: 'Current Bid', sortable: true },
  { key: 'total_bids', label: 'Total Bids', sortable: true },
  { key: 'time_remaining', label: 'Time Remaining', sortable: false },
  { key: 'bidders', label: 'Active Bidders', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Options
const categoryOptions = [
  { label: 'All Categories', value: '' },
  { label: 'Electronics', value: 'electronics' },
  { label: 'Furniture', value: 'furniture' },
  { label: 'Jewelry', value: 'jewelry' },
  { label: 'Art', value: 'art' }
];

// Methods
const fetchAuctions = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    auctions.value = [
      {
        id: 1,
        title: 'Vintage Watch Collection',
        current_bid: '$275.00',
        total_bids: 12,
        time_remaining: '2h 15m',
        bidders: 8,
        status: 'active',
        category: 'jewelry'
      },
      {
        id: 2,
        title: 'Modern Art Painting',
        current_bid: '$1,250.00',
        total_bids: 23,
        time_remaining: '45m',
        bidders: 15,
        status: 'active',
        category: 'art'
      },
      {
        id: 3,
        title: 'Gaming Laptop',
        current_bid: '$850.00',
        total_bids: 18,
        time_remaining: '3h 30m',
        bidders: 12,
        status: 'active',
        category: 'electronics'
      }
    ];
    
    totalItems.value = auctions.value.length;
    totalPages.value = Math.ceil(totalItems.value / perPage.value);
  } catch (err) {
    error.value = 'Failed to load live auctions';
    console.error('Error fetching auctions:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  fetchAuctions();
};

const handleCreate = () => {
  router.push('/admin-spa/auctions/create');
};

const handleView = (auction: any) => {
  router.push(`/admin-spa/auctions/view/${auction.id}`);
};

const handleEdit = (auction: any) => {
  router.push(`/admin-spa/auctions/edit/${auction.id}`);
};

const handleMonitor = (auction: any) => {
  // TODO: Implement real-time monitoring
  showNotification({
    type: 'info',
    title: 'Monitoring Started',
    message: `Now monitoring auction: ${auction.title}`
  });
};

const handleEnd = (auction: any) => {
  auctionToEnd.value = auction;
  showEndModal.value = true;
};

const confirmEnd = async () => {
  if (!auctionToEnd.value) return;
  
  ending.value = true;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    showNotification({
      type: 'success',
      title: 'Auction Ended',
      message: 'Auction has been successfully ended.'
    });
    
    await fetchAuctions();
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'End Failed',
      message: 'Failed to end auction. Please try again.'
    });
  } finally {
    ending.value = false;
    showEndModal.value = false;
    auctionToEnd.value = null;
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  applyFilters();
};

const handleSort = (column: string, direction: string) => {
  filters.sort_by = column;
  filters.sort_direction = direction;
  applyFilters();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchAuctions();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? [...auctions.value] : [];
};

// Auto-refresh every 30 seconds
let refreshInterval: NodeJS.Timeout;

onMounted(() => {
  fetchAuctions();
  
  // Set up auto-refresh
  refreshInterval = setInterval(() => {
    fetchAuctions();
  }, 30000);
});

// Cleanup interval on unmount
import { onUnmounted } from 'vue';
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});
</script>
