<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Trust Auctioneers')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('favicon.png')); ?>">

    <!-- Scripts and Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app-spa.ts']); ?>

    <style>
        /* Vertigo AMS Brand Colors */
        :root {
            --primary-500: #0068ff;
            --primary-600: #0056d6;
            --primary-700: #0045ad;
            --brand-800: #243b53;
            --brand-900: #003472;
        }

        .gradient-primary {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
        }

        .gradient-brand {
            background: linear-gradient(135deg, #243b53 0%, #003472 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .nav-shadow {
            box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
        }

        .mobile-menu {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .mobile-menu.open {
            transform: translateX(0);
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            /* Ensure mobile menu is above everything */
            .customer-mobile-menu {
                position: fixed;
                top: 64px;
                left: 0;
                right: 0;
                z-index: 40;
                max-height: calc(100vh - 64px);
                overflow-y: auto;
            }

            /* Prevent body scroll when mobile menu is open */
            body.mobile-menu-open {
                overflow: hidden;
                position: fixed;
                width: 100%;
            }

            /* Touch-friendly sizing */
            .mobile-touch-target {
                min-height: 44px;
                min-width: 44px;
            }
        }

        /* Safe area support for devices with notches */
        .safe-area-padding {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
        }

        .safe-area-top {
            padding-top: max(1rem, env(safe-area-inset-top));
        }

        /* Improve mobile scrolling */
        .mobile-scroll {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Better mobile tap highlights */
        .mobile-touch-target {
            -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
        }

        .menu-item {
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            transform: translateX(4px);
        }
    </style>

    <!-- Additional head content -->
    <?php echo $__env->yieldPushContent('head'); ?>
</head>
<body class="bg-gray-50">
    <!-- Customer-Facing Navigation -->
    <nav class="glass-effect border-b border-white/20 nav-shadow sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-12 h-12 mr-4">
                            <img src="<?php echo e(asset('assets/img/logo/logo_white_512x512.png')); ?>" alt="Trust Auctioneers Logo" class="w-full h-full object-contain drop-shadow-sm">
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Trust</h1>
                            <p class="text-sm text-gray-600 -mt-1 font-semibold tracking-wide">AUCTIONEERS</p>
                        </div>
                    </div>

                    <!-- Customer Navigation Links -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <!-- Show different links based on authentication status -->
                        <?php if(auth()->guard()->check()): ?>
                        <a href="/spa/register-bid" class="text-gray-600  font-medium transition-all duration-200 hover:border-b-2 hover:border-gray-300 pb-1">
                            Register Bid
                        </a>
                        <a href="/home" class="text-gray-600 border-b-2 border-gray-300   font-medium transition-all duration-200 hover:border-b-2 hover:border-gray-300 pb-1">
                            Home
                        </a>
                        <?php else: ?>
                        <a href="/spa" class="text-gray-600 border-b-2 border-gray-300   font-medium transition-all duration-200 hover:border-b-2 hover:border-gray-300 pb-1">
                            Home
                        </a>
                        <?php endif; ?>

                        <?php if(!auth()->check()): ?>
                        <a href="/admin-login" class="text-gray-600  font-medium transition-all duration-200 hover:border-b-2 hover:border-gray-300 pb-1">
                            Portal
                        </a>
                        <?php endif; ?>

                        <a href="/about" class="text-gray-600  font-medium transition-all duration-200 hover:border-b-2 hover:border-gray-300 pb-1">
                            About
                        </a>
                    </div>
                </div>

                <!-- Right side -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="hidden md:block">
                        <div class="relative">
                            <input type="text" placeholder="Search auctions..." class="w-64 pl-10 pr-4 py-2 bg-white/80 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-gray-200 focus:bg-white">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Watchlist Button -->
                    <a href="#" id="watchlist-nav-icon" class="relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200" title="View your watchlist">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span id="watchlist-badge" class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center" style="display: none;">
                            <span id="watchlist-count">0</span>
                        </span>
                    </a>

                    <!-- Shopping Cart Button -->
                    <a href="/cart" class="relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
                        </svg>
                        <?php $cartCount = \Facades\App\Cache\Repo::notifications() ?>
                        <span id="cart-badge" class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center" style="display: <?php echo e($cartCount > 0 ? 'flex' : 'none'); ?>">
                            <span id="cart-count"><?php echo e($cartCount > 9 ? '9+' : $cartCount); ?></span>
                        </span>
                    </a>

                    <?php if(auth()->user()): ?>
                    <!-- User Profile -->
                    <?php if(!auth()->user()->isSuperAdmin()): ?>
                    <a href="/profile" class="flex items-center text-gray-600 hover:text-primary-600 transition-all duration-200">
                        <svg class="h-5 w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="text-sm"><?php echo e(auth()->user()->name ?? ''); ?></span>
                    </a>
                    <?php endif; ?>

                    <!-- Logout -->
                    <form action="/logout" method="POST" id="logout-form" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="border-2 border-gray-300 text-gray-700 px-4 py-2 rounded-full font-medium hover:border-primary-500 hover:text-primary-600 transition-all duration-200 bg-white shadow-sm hover:shadow-md">
                            Logout
                        </button>
                    </form>
                    <?php else: ?>
                    <!-- User Account for non-authenticated users -->
                    <div class="flex items-center">
                        <button id="desktop-signin-btn" class="border-2 border-gray-300 text-gray-700 px-4 py-2 rounded-full font-medium hover:border-primary-500 hover:text-primary-600 transition-all duration-200 bg-white shadow-sm hover:shadow-md">
                            Sign In
                        </button>
                    </div>
                    <?php endif; ?>

                    <!-- Mobile menu button -->
                    <button
                        id="mobile-menu-toggle"
                        class="md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-white/50 transition-colors duration-200"
                    >
                        <svg id="menu-icon" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg id="close-icon" class="h-6 w-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div
            id="mobile-menu"
            class="md:hidden customer-mobile-menu border-t border-gray-200 bg-white/95 backdrop-blur-sm shadow-lg hidden"
        >
            <div class="px-4 py-4 space-y-3 safe-area-padding">
                <!-- Mobile Navigation Links -->
                <?php if(auth()->guard()->check()): ?>
                <a href="/spa/register-bid" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                    Register Bid
                </a>
                <a href="/home" class="block px-4 py-3 text-primary-600 font-medium bg-primary-50 rounded-lg mobile-touch-target">
                    Home
                </a>
                <?php else: ?>
                <a href="/spa" class="block px-4 py-3 text-primary-600 font-medium bg-primary-50 rounded-lg mobile-touch-target">
                    Home
                </a>
                <?php endif; ?>

                <a href="/about" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target">
                    About
                </a>

                <!-- Mobile Search -->
                <div class="pt-3 border-t border-gray-200">
                    <div class="relative">
                        <input type="text" placeholder="Search auctions..." class="w-full pl-10 pr-4 py-3 bg-white border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Mobile User Actions -->
                <div class="pt-3 border-t border-gray-200">
                    <?php if(auth()->user()): ?>
                    <!-- Authenticated user options -->
                    <?php if(!auth()->user()->isSuperAdmin()): ?>
                    <a href="/profile" class="block px-4 py-3 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 mobile-touch-target mb-3">
                        <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Profile (<?php echo e(auth()->user()->name ?? ''); ?>)
                    </a>
                    <?php endif; ?>
                    <form action="/logout" method="POST" class="w-full">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="w-full border-2 border-gray-300 text-gray-700 px-4 py-3 rounded-lg font-medium hover:border-primary-500 hover:text-primary-600 transition-colors duration-200 mobile-touch-target bg-white shadow-sm text-center">
                            Logout
                        </button>
                    </form>
                    <?php else: ?>
                    <!-- Non-authenticated user options -->
                    <button id="mobile-signin-btn" class="w-full border-2 border-gray-300 text-gray-700 px-4 py-3 rounded-lg font-medium hover:border-primary-500 hover:text-primary-600 transition-colors duration-200 mobile-touch-target bg-white shadow-sm text-center">
                        Sign In
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Vue SPA Mount Point -->
    <div id="app">
        <!-- Loading fallback while Vue loads -->
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading Trust Auctioneers...</p>
            </div>
        </div>
    </div>

    <!-- Mobile Menu JavaScript -->
    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    const isOpen = !mobileMenu.classList.contains('hidden');

                    if (isOpen) {
                        // Close menu
                        mobileMenu.classList.add('hidden');
                        menuIcon.classList.remove('hidden');
                        closeIcon.classList.add('hidden');
                        document.body.classList.remove('mobile-menu-open');
                    } else {
                        // Open menu
                        mobileMenu.classList.remove('hidden');
                        menuIcon.classList.add('hidden');
                        closeIcon.classList.remove('hidden');
                        document.body.classList.add('mobile-menu-open');
                    }
                });
            }
        });
    </script>

    <!-- Global data for Vue -->
    <script>
        // Pass server data to Vue
        try {
            window.branches = <?php echo json_encode(\App\Models\Branch::all() ?? [], 15, 512) ?>;
            // Load all adverts from database for banner slider
            window.adverts = <?php echo json_encode(\App\Models\Advert::all() ?? [], 15, 512) ?>;
            window.user = <?php echo json_encode(auth()->user()?->load('roles') ?? null, 15, 512) ?>;
            // Load cart data from session
            window.cartItems = <?php echo json_encode(\Facades\App\Cache\Repo::cart() ?? [], 15, 512) ?>;
        } catch (e) {
            window.branches = [];
            window.adverts = [];
            window.user = null;
            window.cartItems = [];
            console.warn('Error loading server data:', e);
        }

        // CSRF token for axios
        if (typeof axios !== 'undefined') {
            window.axios = axios;
        } else if (typeof require !== 'undefined') {
            window.axios = require('axios');
        }

        if (window.axios) {
            window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            window.axios.defaults.withCredentials = true;
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                window.axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
            }
        }

        // Listen for cart updates and update navigation badge
        window.addEventListener('cart-updated', function(event) {
            const cartBadge = document.getElementById('cart-badge');
            const cartCount = document.getElementById('cart-count');

            if (cartBadge && cartCount) {
                const count = event.detail.count;

                if (count > 0) {
                    cartBadge.style.display = 'flex';
                    cartCount.textContent = count > 9 ? '9+' : count.toString();
                } else {
                    cartBadge.style.display = 'none';
                }
            }
        });

        // Listen for watchlist updates and update navigation badge
        window.addEventListener('watchlist-updated', function(event) {
            const watchlistBadge = document.getElementById('watchlist-badge');
            const watchlistCount = document.getElementById('watchlist-count');

            if (watchlistBadge && watchlistCount) {
                const count = event.detail.count;

                if (count > 0) {
                    watchlistBadge.style.display = 'flex';
                    watchlistCount.textContent = count > 9 ? '9+' : count.toString();
                } else {
                    watchlistBadge.style.display = 'none';
                }
            }
        });

        // Handle sign in button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const desktopSigninBtn = document.getElementById('desktop-signin-btn');
            const mobileSigninBtn = document.getElementById('mobile-signin-btn');

            function showAuthModal() {
                // Dispatch custom event that Vue app can listen to
                window.dispatchEvent(new CustomEvent('show-auth-modal', {
                    detail: { tab: 'login' }
                }));
            }

            if (desktopSigninBtn) {
                desktopSigninBtn.addEventListener('click', showAuthModal);
            }

            if (mobileSigninBtn) {
                mobileSigninBtn.addEventListener('click', showAuthModal);
            }
        });


    </script>

    <!-- Additional scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/layouts/spa-layout.blade.php ENDPATH**/ ?>