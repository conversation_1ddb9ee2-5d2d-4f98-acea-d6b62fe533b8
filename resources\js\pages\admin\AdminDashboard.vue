<template>
  <AdminPageTemplate
    title="Dashboard Overview"
    subtitle="Monitor your auction platform performance"
    :loading="loading"
    :error="error"
  >
    <template #actions>
      <div class="flex items-center space-x-3">
        <!-- Last refresh indicator -->
        <div v-if="lastRefresh" class="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-2">
          <div
            :class="[
              'w-2 h-2 rounded-full',
              loading ? 'bg-yellow-400 animate-pulse' : 'bg-green-400'
            ]"
          ></div>
          <span>{{ formatLastRefresh }}</span>
        </div>

        <Button variant="outline" size="sm" @click="exportData">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="primary" size="sm" @click="createAuction">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          Create Auction
        </Button>
        <Button variant="outline" size="sm" @click="handleRefresh" :loading="refreshing">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </Button>
      </div>
    </template>

    <!-- Dashboard Filters -->
    <DashboardFilters
      :loading="loading"
      @filter-change="handleFilterChange"
      @refresh="handleRefresh"
      @export="exportData"
      class="mb-6"
    />

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <AdminStatsCard
        title="Active Auctions"
        :value="stats.activeAuctions"
        :change="stats.activeAuctionsChange"
        icon="auctions"
        color="blue"
        :loading="loading"
      />
      <AdminStatsCard
        title="Total Revenue"
        :value="dashboardStore.totalRevenueCurrency"
        :change="stats.revenueChange"
        icon="revenue"
        color="green"
        :loading="loading"
      />
      <AdminStatsCard
        title="Total Items"
        :value="stats.totalItems"
        :change="stats.itemsChange"
        icon="items"
        color="yellow"
        :loading="loading"
      />
      <AdminStatsCard
        title="Registered Users"
        :value="stats.totalUsers"
        :change="stats.usersChange"
        icon="users"
        color="purple"
        :loading="loading"
      />
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Activity -->
      <div class="lg:col-span-2">
        <Card class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Recent Activity</h3>
            <router-link
              to="/admin-spa/activity"
              class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              View all
            </router-link>
          </div>
          <div class="space-y-3">
            <div
              v-for="activity in recentActivity"
              :key="activity.id"
              class="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <div class="flex-shrink-0">
                <div
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium',
                    activity.type === 'auction_ended' ? 'bg-green-500' :
                    activity.type === 'bid_placed' ? 'bg-blue-500' :
                    activity.type === 'user_registered' ? 'bg-purple-500' : 'bg-gray-500'
                  ]"
                >
                  {{ activity.user?.name?.charAt(0) || '?' }}
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ activity.title }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ activity.description }}</p>
                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  {{ formatTime(activity.timestamp) }}
                </p>
              </div>
            </div>
          </div>
          <div v-if="recentActivity.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            No recent activity
          </div>
        </Card>
      </div>

      <!-- Notifications -->
      <div>
        <NotificationCenter
          :branch-id="currentFilters.branch_id"
          :auto-refresh="true"
          :refresh-interval="60000"
        />
      </div>
    </div>

    <!-- Quick Actions -->
    <QuickActions />
  </AdminPageTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { AdminPageTemplate, AdminStatsCard, useAdminDashboard } from '@/components/admin';
import { Button, Card, Badge } from '@/components/ui';
import { useAuthStore } from '@/stores/auth';
import DashboardFilters from '@/components/admin/dashboard/DashboardFilters.vue';
import NotificationCenter from '@/components/admin/dashboard/NotificationCenter.vue';
import QuickActions from '@/components/admin/dashboard/QuickActions.vue';

// Router
const router = useRouter();

// Store
const dashboardStore = useAdminDashboard();
const authStore = useAuthStore();

// State
const refreshing = ref(false);
const currentFilters = ref({
  date_from: '',
  date_to: '',
  branch_id: undefined as number | undefined
});

// Computed properties from store
const loading = computed(() => dashboardStore.isLoading);
const error = computed(() => dashboardStore.error);
const stats = computed(() => dashboardStore.stats);
const recentActivity = computed(() => dashboardStore.recentActivity);
const systemStatus = computed(() => dashboardStore.systemStatus);
const lastRefresh = computed(() => dashboardStore.lastRefresh);

const formatLastRefresh = computed(() => {
  if (!lastRefresh.value) return '';
  const now = new Date();
  const diff = now.getTime() - lastRefresh.value.getTime();
  const minutes = Math.floor(diff / 60000);

  if (minutes < 1) return 'Just now';
  if (minutes === 1) return '1 minute ago';
  if (minutes < 60) return `${minutes} minutes ago`;

  const hours = Math.floor(minutes / 60);
  if (hours === 1) return '1 hour ago';
  if (hours < 24) return `${hours} hours ago`;

  return lastRefresh.value.toLocaleDateString();
});

// Mock data for recent auctions (until we have real data)
const recentAuctions = ref([
  {
    id: 1,
    title: 'Vintage Watch Collection',
    status: 'Ends in 2 hours',
    currentBid: 1250
  },
  {
    id: 2,
    title: 'Art Deco Furniture',
    status: 'Ends in 1 day',
    currentBid: 850
  },
  {
    id: 3,
    title: 'Classic Car Parts',
    status: 'Ends in 3 days',
    currentBid: 2100
  }
]);

// Auto-refresh interval
let refreshInterval: number | null = null;

// Methods
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const handleRefresh = async () => {
  refreshing.value = true;
  try {
    await dashboardStore.fetchDashboardData(true, currentFilters.value);
  } catch (err) {
    console.error('Failed to refresh dashboard:', err);
  } finally {
    refreshing.value = false;
  }
};

const handleFilterChange = async (filters: any) => {
  currentFilters.value = { ...filters };
  await dashboardStore.fetchDashboardData(true, filters);
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString();
};

const setupAutoRefresh = () => {
  if (dashboardStore.autoRefresh && !refreshInterval) {
    refreshInterval = window.setInterval(() => {
      dashboardStore.fetchDashboardData();
    }, dashboardStore.refreshInterval);
  }
};

const clearAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
};

const exportData = () => {
  // Implement export functionality
  console.log('Exporting dashboard data...');
};

// Lifecycle hooks
onMounted(async () => {
  try {
    console.log('AdminDashboard mounted');
    console.log('Current user:', authStore.user);
    console.log('Is authenticated:', authStore.isAuthenticated);
    console.log('Session auth:', authStore.sessionAuth);

    await dashboardStore.initialize();
    setupAutoRefresh();
  } catch (err) {
    console.error('Failed to initialize dashboard:', err);
  }
});

onUnmounted(() => {
  clearAutoRefresh();
});

const createAuction = () => {
  router.push('/admin-spa/auctions/create');
};

// Lifecycle
onMounted(() => {
  // Load initial data
  // In real app, this would fetch from API
});

// Page meta for router
defineOptions({
  meta: {
    title: 'Dashboard Overview',
    subtitle: 'Monitor your auction platform performance'
  }
});
</script>
