<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 300px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Login Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="admin" required>
        </div>
        
        <button type="submit">Login</button>
        <button type="button" onclick="testAfterLogin()">Test Admin APIs</button>
        <button type="button" onclick="clearResults()">Clear Results</button>
    </form>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Get CSRF token
        async function getCsrfToken() {
            try {
                await fetch('/sanctum/csrf-cookie', {
                    credentials: 'include'
                });
                
                // Try to get token from meta tag or make a request to get it
                const response = await fetch('/login', {
                    credentials: 'include'
                });
                const html = await response.text();
                const match = html.match(/name="csrf-token" content="([^"]+)"/);
                return match ? match[1] : null;
            } catch (error) {
                console.error('Failed to get CSRF token:', error);
                return null;
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                addResult('Getting CSRF token...', 'info');
                const csrfToken = await getCsrfToken();
                
                if (!csrfToken) {
                    addResult('❌ Failed to get CSRF token', 'error');
                    return;
                }
                
                addResult('Attempting login...', 'info');
                
                const formData = new FormData();
                formData.append('email', email);
                formData.append('password', password);
                formData.append('_token', csrfToken);
                
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    addResult(`✅ Login successful! Welcome ${result.user?.name || 'User'}`, 'success');
                    addResult(`<pre>${JSON.stringify(result, null, 2)}</pre>`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Login failed: ${response.status} ${response.statusText}`, 'error');
                    addResult(`Response: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Login error: ${error.message}`, 'error');
            }
        });

        async function testAfterLogin() {
            try {
                addResult('Testing session authentication...', 'info');
                
                const sessionResponse = await fetch('/api/user-session', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (sessionResponse.ok) {
                    const user = await sessionResponse.json();
                    addResult(`✅ Session auth successful! User: ${user.name} (${user.email})`, 'success');
                    
                    // Test admin access
                    addResult('Testing admin access...', 'info');
                    const adminResponse = await fetch('/api/admin/check-access', {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    if (adminResponse.ok) {
                        const adminResult = await adminResponse.json();
                        if (adminResult.hasAccess) {
                            addResult(`✅ Admin access granted!`, 'success');
                            
                            // Test admin users API
                            addResult('Testing admin users API...', 'info');
                            const usersResponse = await fetch('/api/admin/users?page=1&perPage=5', {
                                method: 'GET',
                                credentials: 'include',
                                headers: {
                                    'Accept': 'application/json',
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            });

                            if (usersResponse.ok) {
                                const usersResult = await usersResponse.json();
                                addResult(`✅ Admin users API successful! Found ${usersResult.data?.length || 0} users`, 'success');
                            } else {
                                const errorText = await usersResponse.text();
                                addResult(`❌ Admin users API failed: ${usersResponse.status}`, 'error');
                                addResult(`Response: ${errorText}`, 'error');
                            }
                        } else {
                            addResult(`❌ Admin access denied: ${adminResult.reason}`, 'error');
                        }
                    } else {
                        const errorText = await adminResponse.text();
                        addResult(`❌ Admin access check failed: ${adminResponse.status}`, 'error');
                        addResult(`Response: ${errorText}`, 'error');
                    }
                } else {
                    addResult(`❌ Session auth failed: ${sessionResponse.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Test error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
