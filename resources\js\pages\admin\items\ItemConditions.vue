<template>
  <AdminListTemplate
    title="Item Conditions"
    subtitle="Manage item condition standards"
    :loading="loading"
    :error="error"
    :items="conditions"
    :columns="columns"
    :selected-items="selectedItems"
    :show-bulk-actions="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    :per-page="perPage"
    create-button-text="Add Condition"
    empty-state-title="No conditions found"
    empty-state-message="Get started by adding your first item condition."
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
    @select-all="handleSelectAll"
    @bulk-delete="handleBulkDelete"
    @view="handleView"
    @edit="handleEdit"
    @delete="handleDelete"
    @refresh="fetchConditions"
  >
    <template #filters>
      <Select
        v-model="filters.status"
        :options="statusOptions"
        placeholder="Filter by status"
        class="w-48"
        @change="applyFilters"
      />
    </template>
  </AdminListTemplate>

  <!-- Delete Confirmation Modal -->
  <AdminModal
    v-model="showDeleteModal"
    title="Delete Condition"
    :loading="deleting"
    @confirm="confirmDelete"
    @cancel="showDeleteModal = false"
  >
    <p class="text-gray-600">
      Are you sure you want to delete this condition? This action cannot be undone.
    </p>
  </AdminModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin/templates';
import { AdminModal } from '@/components/admin/ui';
import { Select } from '@/components/ui';
import { useNotifications } from '@/composables/useNotifications';

// Router
const router = useRouter();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const conditions = ref([]);
const selectedItems = ref([]);
const showDeleteModal = ref(false);
const deleting = ref(false);
const itemToDelete = ref<any>(null);

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(25);

// Filters
const filters = reactive({
  search: '',
  status: '',
  sort_by: 'name',
  sort_direction: 'asc'
});

// Table columns
const columns = [
  { key: 'name', label: 'Condition Name', sortable: true },
  { key: 'description', label: 'Description', sortable: false },
  { key: 'grade', label: 'Grade', sortable: true },
  { key: 'items_count', label: 'Items', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Created', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Options
const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' }
];

// Methods
const fetchConditions = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    conditions.value = [
      {
        id: 1,
        name: 'Mint',
        description: 'Perfect condition, like new',
        grade: 'A+',
        items_count: 12,
        status: 'active',
        created_at: '2024-01-15'
      },
      {
        id: 2,
        name: 'Excellent',
        description: 'Very good condition with minimal wear',
        grade: 'A',
        items_count: 28,
        status: 'active',
        created_at: '2024-01-10'
      },
      {
        id: 3,
        name: 'Good',
        description: 'Good condition with some signs of use',
        grade: 'B',
        items_count: 45,
        status: 'active',
        created_at: '2024-01-08'
      },
      {
        id: 4,
        name: 'Fair',
        description: 'Fair condition with noticeable wear',
        grade: 'C',
        items_count: 23,
        status: 'active',
        created_at: '2024-01-05'
      },
      {
        id: 5,
        name: 'Poor',
        description: 'Poor condition, significant wear or damage',
        grade: 'D',
        items_count: 8,
        status: 'active',
        created_at: '2024-01-01'
      }
    ];
    
    totalItems.value = conditions.value.length;
    totalPages.value = Math.ceil(totalItems.value / perPage.value);
  } catch (err) {
    error.value = 'Failed to load conditions';
    console.error('Error fetching conditions:', err);
  } finally {
    loading.value = false;
  }
};

const applyFilters = () => {
  fetchConditions();
};

const handleCreate = () => {
  router.push('/admin-spa/items/conditions/create');
};

const handleView = (condition: any) => {
  router.push(`/admin-spa/items/conditions/view/${condition.id}`);
};

const handleEdit = (condition: any) => {
  router.push(`/admin-spa/items/conditions/edit/${condition.id}`);
};

const handleDelete = (condition: any) => {
  itemToDelete.value = condition;
  showDeleteModal.value = true;
};

const confirmDelete = async () => {
  if (!itemToDelete.value) return;
  
  deleting.value = true;
  
  try {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    showNotification({
      type: 'success',
      title: 'Condition Deleted',
      message: 'Condition has been successfully deleted.'
    });
    
    await fetchConditions();
  } catch (err) {
    showNotification({
      type: 'error',
      title: 'Delete Failed',
      message: 'Failed to delete condition. Please try again.'
    });
  } finally {
    deleting.value = false;
    showDeleteModal.value = false;
    itemToDelete.value = null;
  }
};

const handleSearch = (query: string) => {
  filters.search = query;
  applyFilters();
};

const handleSort = (column: string, direction: string) => {
  filters.sort_by = column;
  filters.sort_direction = direction;
  applyFilters();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchConditions();
};

const handleSelectAll = (selected: boolean) => {
  selectedItems.value = selected ? [...conditions.value] : [];
};

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return;
  
  // TODO: Implement bulk delete
  showNotification({
    type: 'info',
    title: 'Bulk Delete',
    message: 'Bulk delete functionality will be implemented soon.'
  });
};

// Initialize
onMounted(() => {
  fetchConditions();
});
</script>
